import hashlib
import os
import random
import string
import tempfile

KB = 1024


# From https://stackoverflow.com/a/519653
def read_in_chunks(file_object, chunk_size=64 * KB):
    """Read a file in chunks, yielding a chunk each iteration

    Example Usage:
        with open(file, 'rb') as f:
            for chunk in read_in_chunks(f):
                process_data(chunk)
    """
    while True:
        data = file_object.read(chunk_size)
        if not data:
            break
        yield data


def hash_file(file):
    """Creates a sha256 hash of a file

    This function can handle files of any size

    Args:
        file: an open Django File Object, or another open file-like object to be hashed

    Returns:
        string: the hexdigest of the hash
    """
    sha256 = hashlib.sha256()

    try:
        # go through the file and hash
        for chunk in file.chunks():
            sha256.update(chunk)
    except AttributeError:
        # no chunks(), so this is not a Django file
        for chunk in read_in_chunks(file):
            sha256.update(chunk)

    return sha256.hexdigest()


def generate_random_filename(suffix=None, prefix="", dir=None):
    """
    Generate a random filename.

    Args:
      suffix (str): File extension. Example: ".pdf".
      prefix (str, optional): Prefix to be added to the filename. Defaults to ''.
      dir (str, optional): Directory where the file will be created. Defaults to None.

    Returns:
      str: Random filename.
      example: myfile_g_o18d1e.pdf

    """
    with tempfile.NamedTemporaryFile(
        delete=True, suffix=suffix, prefix=prefix, dir=dir
    ) as temp_file:
        random_filename = os.path.basename(temp_file.name)
    return random_filename


def generate_random_path(path):
    dir_name = os.path.dirname(path)
    file_name, file_extension = os.path.splitext(os.path.basename(path))

    random_str = "".join(random.choices(string.ascii_lowercase + string.digits, k=6))

    new_file_name = f"{file_name}_{random_str}{file_extension}"

    return os.path.join(dir_name, new_file_name)
