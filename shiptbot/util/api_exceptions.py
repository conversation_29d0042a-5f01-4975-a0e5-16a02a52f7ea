import uuid
from datetime import datetime

from rest_framework import exceptions, status
from rest_framework.exceptions import APIException
from rest_framework.serializers import ValidationError

from shiptbot.util.const_namespace import BaseConstNamespace

# https://en.wikipedia.org/wiki/List_of_HTTP_status_codes
# http://www.django-rest-framework.org/api-guide/status-codes/

# 200 - OK
# 201 - Created

# 301 - Moved permanently
# 302 - Moved temporarily
# 304 - Not Modified

# 400 - Bad Request
# 401 - Unauthorized
# 403 - Forbidden
# 404 - Not Found

# 500 - Internal Server Error
# 503 - Service Unavailable

# Default base error schema
DEF_EXCEPTION_MESSAGE = "An error occurred"
DEF_EXCEPTION_DETAILS = (
    "An error occurred while processing your request. Please try again later."
)
DEF_EXCEPTION_CODE = "ERROR"
DEF_EXCEPTION_STATUS_CODE = status.HTTP_500_INTERNAL_SERVER_ERROR


class BaseErrorSchema:
    """
    BaseErrorSchema is a class that represents the schema for error responses.

    Attributes:
        code (str): The error code. Defaults to DEF_EXCEPTION_CODE.
        status_code (int): The HTTP status code for the error. Defaults to DEF_EXCEPTION_STATUS_CODE.
        message (str): The error message. Defaults to DEF_EXCEPTION_MESSAGE.
        details (str): Additional details about the error. Defaults to DEF_EXCEPTION_DETAILS.
        timestamp (str): The ISO 8601 formatted timestamp when the error occurred.
        request_id (str): A unique identifier for the request.

    Methods:
        get_error_response: Returns a dictionary representing the error response.
    """

    def __init__(
        self,
        code: str = DEF_EXCEPTION_CODE,
        status_code: int = DEF_EXCEPTION_STATUS_CODE,
        message: str = DEF_EXCEPTION_MESSAGE,
        details: str = DEF_EXCEPTION_DETAILS,
    ):
        """
        Initializes the exception with the provided parameters.

        Args:
            code (str): The exception code. Defaults to DEF_EXCEPTION_CODE.
            status_code (int): The HTTP status code for the exception. Defaults to DEF_EXCEPTION_STATUS_CODE.
            message (str): A message describing the exception. Defaults to DEF_EXCEPTION_MESSAGE.
            details (str): Additional details about the exception. Defaults to DEF_EXCEPTION_DETAILS.
        """
        self.code = code
        self.status_code = status_code
        self.message = message
        self.details = details
        self.timestamp = datetime.now().isoformat()
        self.request_id = str(uuid.uuid4())

    @property
    def get_error_response(self):
        """
        Generates a structured error response dictionary.

        Returns:
            dict: A dictionary containing the error response with the following keys:
                - status_code (int): The HTTP status code of the error.
                - error (dict): A dictionary containing error details:
                    - code (str): The error code.
                    - message (str): A descriptive error message.
                    - details (str): Additional details about the error.
                - request_id (str): The unique identifier for the request.
                - timestamp (str): The timestamp when the error occurred.
        """
        return {
            "status_code": self.status_code,
            "error": {
                "code": self.code,
                "message": self.message,
                "details": self.details,
            },
            "request_id": self.request_id,
            "timestamp": self.timestamp,
        }


class BlueinkVaultAPIException(APIException):
    """
    Custom exception class for Blueform  API.

    Args:
        error_schema (BaseErrorSchema, optional): An instance of BaseErrorSchema to define the error structure. Defaults to None.
        details (str, optional): Additional details about the error. If provided, it will be set in the error_schema. Defaults to None.

    Attributes:
        error_schema (BaseErrorSchema): The schema defining the error structure.

    Inherits from:
        APIException: The base exception class for API-related errors.
    """

    def __init__(self, error_schema: BaseErrorSchema = None, details=None):
        """
        Initializes the exception with an optional error schema and details.

        Args:
            error_schema (BaseErrorSchema, optional): An instance of BaseErrorSchema. If not provided, a new instance will be created.
            details (Any, optional): Additional details to be added to the error schema.

        Attributes:
            error_schema (BaseErrorSchema): The error schema associated with the exception.
        """
        if error_schema is None:
            error_schema = BaseErrorSchema()

        if details is not None:
            error_schema.details = details

        self.error_schema = error_schema

        super().__init__(error_schema)


class APIForbiddenError(exceptions.PermissionDenied):
    pass


class APIUsageExceeded(exceptions.PermissionDenied):
    default_detail = "You have used up your allotment of performing this action"
    default_code = "usage_exceeded"


class APIBadRequestError(exceptions.APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Bad Request"


class APINotFoundError(exceptions.NotFound):
    pass


class APIValidationError(ValidationError):
    def __init__(self, detail=None, code=None, parent_field=None):
        self.parent_field = parent_field
        return super().__init__(detail, code)


class APIServiceNotAvailableError(exceptions.APIException):
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    default_detail = "Service Not Available"
    default_code = "service_unavailable"


class APIConflictError(exceptions.APIException):
    """Server is in a conflicted state, or requested resource is in a conflicted state"""

    status_code = status.HTTP_409_CONFLICT
    default_detail = "Conflict"
    default_code = "conflict"


class ErrorCodeNameSpace(BaseConstNamespace):
    AUTHENTICATION_FAILED = exceptions.AuthenticationFailed.default_code
    CONFLICT = APIConflictError.default_code
    ERROR = exceptions.APIException.default_code
    INVALID = APIValidationError.default_code
    METHOD_NOT_ALLOWED = exceptions.MethodNotAllowed.default_code
    NOT_ACCEPTABLE = exceptions.NotAcceptable.default_code
    NOT_AUTHENTICATED = exceptions.NotAuthenticated.default_code
    NOT_FOUND = APINotFoundError.default_code
    PERMISSION_DENIED = APIForbiddenError.default_code
    USAGE_EXCEEDED = APIUsageExceeded.default_code
    PARSE_ERROR = exceptions.ParseError.default_code
    SERVICE_UNAVAILABLE = APIServiceNotAvailableError.default_code
    THROTTLED = exceptions.Throttled.default_code
    UNSUPPORTED_MEDIA_TYPE = exceptions.UnsupportedMediaType.default_code


# These Error Codes mostly follow the default_codes specified on Django Rest Framework
# exceptions. However, we add CONFLICT and SERVICE_UNAVAILABLE
ERROR_CODE = ErrorCodeNameSpace()

# This value is hardcoded in the Django REST Framework default exception handler
DRF_GENERIC_DETAIL_KEY = "detail"
DEFAULT_ERROR_CODE = ERROR_CODE.ERROR
DEFAULT_ERROR_MESSAGE = "Error."
