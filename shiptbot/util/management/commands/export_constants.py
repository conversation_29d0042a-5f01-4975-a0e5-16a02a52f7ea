import os
import subprocess
from shutil import copyfile

import simplejson
from django.conf import settings
from django.core.management.base import BaseCommand, CommandError

from shiptbot.util.constants import exports

DEFAULT_CONSTANTS_FOLDER = str(settings.ROOT_DIR.path("frontend/src/constants"))
ESLINT_ABSPATH = str(settings.ROOT_DIR.path("frontend/node_modules/.bin/eslint"))

JS_CONSTANTS_FILENAME = "backend.js"
JS_BUILD_FILENAME = "_constants.build.js"


class Command(BaseCommand):
    help = "Generate a Javascript constants file from our Python constants"

    def add_arguments(self, parser):
        parser.add_argument(
            "--fake", action="store_true", default=False, help="Do a test run"
        )

        parser.add_argument(
            "--output-dir",
            type=str,
            default=DEFAULT_CONSTANTS_FOLDER,
            help="The path to save the created JS file to",
        )

        parser.add_argument(
            "--eslint",
            action="store_true",
            default=False,
            help="Run eslint on the generated output",
        )

    def handle(self, *args, **options):
        self.fake = options["fake"]
        self.run_eslint = options["eslint"]
        constants_dir = options["output_dir"]

        constants_dir = os.path.abspath(constants_dir)
        if not os.path.isdir(constants_dir):
            raise CommandError("Directory {} does not exist".format(constants_dir))

        build_file_abspath = os.path.join(constants_dir, JS_BUILD_FILENAME)
        out_file_abspath = os.path.join(constants_dir, JS_CONSTANTS_FILENAME)

        # set up file
        if not self.fake:
            # try to remove any build file - this will only exist if there was a problem when building before
            try:
                os.remove(build_file_abspath)
            except OSError:
                # ignore does not exist error
                pass

            # open a new file
            file = open(build_file_abspath, "w")

        for name, value in exports.items():
            print("{}... ".format(name), end="")
            output = "export const {} = {};\n\n".format(
                name, simplejson.dumps(value, sort_keys=True, indent=4 * " ")
            )
            print("done")

            if self.fake:
                print(output)
            else:
                file.write(output)

        if not self.fake:
            file.close()
            copyfile(build_file_abspath, out_file_abspath)
            os.remove(build_file_abspath)

            if self.run_eslint:
                cmd = "{} --fix {}".format(ESLINT_ABSPATH, out_file_abspath)
                exit_code = subprocess.call(cmd, shell=True)
                if exit_code != 0:
                    raise CommandError(
                        "Could not run eslint on generated constants file.\n"
                        "The eslint binary must be available at {}.\n"
                        "Try running this command outside of a Docker container. "
                        "Or manually run:\n$ eslint --fix {} ".format(
                            ESLINT_ABSPATH, out_file_abspath
                        )
                    )
