import uuid

import base58
from django.db import models

_BUID_PREFIX_MAX_LEN = 5
# Separator between the BUID prefix and the unique ID part or the BUID
_BUID_SEP: str = "_"

DEFAULT_BUID_OBJ_CODE = "buid"


class BUIDCode:
    ACCOUNT = "act"
    USER = "usr"
    SETTING = "sett"
    OFFER = "offer"
    STORE = "store"
    DAY_RANGE = "dr"
    DAY = "day"
    RANGE = "range"
    ZONE = "zone"
    SESSION = "ses"
    SUBSCRIPTION = "sub"


def get_encodable_id():
    """Get a unique attribute of self that is ready to be encoded by base58.b58encode()

    This can handle string, int and UUID attributes by default. This method should
    be overridden if the attribute does not fal into those categories.
    """
    return uuid.uuid4().bytes


def buid_prefix(obj_code):
    """Return the BUID_OBJ_CODE plus the separator"""
    return f"{obj_code}{_BUID_SEP}"


def generate_buid(obj_code):
    """Return an object id derived from the UUID id and OBJ_PREFIX"""
    b58_id_str = base58.b58encode(get_encodable_id()).decode()
    return f"{buid_prefix(obj_code)}{b58_id_str}"


class BUIDField(models.CharField):
    def __init__(self, obj_code=None, *args, **kwargs):
        # Set the length and random_func as instance variables
        length = _BUID_PREFIX_MAX_LEN + 30
        self.length = length
        kwargs["max_length"] = length
        super().__init__(*args, **kwargs)
