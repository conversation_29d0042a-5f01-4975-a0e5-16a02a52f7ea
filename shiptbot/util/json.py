import collections
from datetime import date, datetime, time

import simplejson


def json_dump(python_object, indents=None, on_error=None, **kwargs):
    """
    Takes any Python value that is serializable by JSON and dumps it as a string.
    """

    def make_json(obj):
        """
        Handles some things that aren't JSON serializable like dates, datetimes, etc.
        """
        if hasattr(obj, "to_dict"):
            # If we've made a Django base model that supports it, use it.
            return obj.to_dict()

        elif isinstance(obj, collections.Set):
            # If it's a set, make it a list.
            return list(obj)

        elif (
            isinstance(obj, date) or isinstance(obj, datetime) or isinstance(obj, time)
        ):
            # Make all datetimes into strings first.
            return str(obj)

        elif hasattr(obj, "__call__"):
            # Don't try to dump functions.
            return ""
        raise TypeError("{} is not serializable into JSON".format(repr(obj)))

    try:
        return simplejson.dumps(
            python_object,
            default=make_json,
            indent=indents,
            namedtuple_as_object=False,
            **kwargs
        )
    except (TypeError, ValueError):
        return simplejson.dumps(on_error or {})
