import html

import yaml


def prettify_for_admin(short_description=""):
    """Decorator for an ModelAdmin method to display data (e.g. from a JSONField) prettily

    Usage:

    @admin.register(SomeModel)
    class SomeModelAdmin(admin.ModelAdmin):
        # ... other ModelAdmin config

        readonly_fields = ('metadata_pretty',)

        @prettify_for_admin('Metadata')
        def metadata_pretty(self, obj):
            return obj.metadata

    """

    def prettify_decorator(func):
        def func_wrapper(self, obj):
            value = func(self, obj)
            formatted = "<pre>{}</pre>".format(html.escape(yaml.dump(value)))

            return formatted

        func_wrapper.allow_tags = True

        if short_description:
            sd = short_description
        else:
            sd = func.__name__.replace("_", " ")
        func_wrapper.short_description = sd

        return func_wrapper

    return prettify_decorator
