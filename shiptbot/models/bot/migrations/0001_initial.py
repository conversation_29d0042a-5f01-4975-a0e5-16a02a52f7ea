# Generated by Django 5.0.7 on 2025-07-24 01:46

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import shiptbot.util.buids


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BotSetting",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "refresh_rate_seconds",
                    models.IntegerField(
                        default=2,
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                ("stop_after_accepted", models.BooleanField(default=False)),
                ("stop_after_missed", models.BooleanField(default=False)),
                ("notis_after_accept", models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ("notis_afer_missed", models.<PERSON>olean<PERSON>ield(default=False)),
                ("notis_via_email", models.BooleanField(default=False)),
                ("notis_via_telegram", models.BooleanField(default=False)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Day",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("enabled", models.BooleanField(default=True)),
                (
                    "day",
                    models.CharField(
                        choices=[
                            ("fri", "Friday"),
                            ("mon", "Monday"),
                            ("sat", "Saturday"),
                            ("sun", "Sunday"),
                            ("thu", "Thursday"),
                            ("tue", "Tuesday"),
                            ("wed", "Wednesday"),
                        ],
                        max_length=3,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "day")},
            },
        ),
        migrations.CreateModel(
            name="Session",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ("-started_at",),
            },
        ),
        migrations.CreateModel(
            name="Offer",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("ac", "Accepted"), ("go", "Gone"), ("sk", "Skipped")],
                        default="sk",
                        max_length=2,
                    ),
                ),
                ("offer_data", models.JSONField(blank=True, default=dict)),
                (
                    "skip_reason",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="bot.session",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
            },
        ),
        migrations.CreateModel(
            name="Zone",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("zone_id", models.CharField(max_length=20)),
                ("zone_name", models.CharField(max_length=100)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Range",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("start_time", models.TimeField()),
                ("end_time", models.TimeField()),
                ("overwrite_store_min_pay", models.BooleanField(default=False)),
                (
                    "min_pay",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("overwrite_store_distance", models.BooleanField(default=False)),
                (
                    "min_distance_miles",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "max_distance_miles",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "day",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="bot.day"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("start_time", "end_time", "day", "user")},
            },
        ),
        migrations.CreateModel(
            name="Store",
            fields=[
                (
                    "id",
                    shiptbot.util.buids.BUIDField(
                        blank=True,
                        editable=False,
                        max_length=35,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("store_id", models.CharField(max_length=20)),
                ("store_name", models.CharField(max_length=100)),
                ("store_data", models.JSONField(blank=True, default=dict)),
                ("enabled", models.BooleanField(default=False)),
                (
                    "min_pay",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "min_pay_per_mile",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "min_distance_miles",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "max_distance_miles",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "zone",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="bot.zone"
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "store_id")},
            },
        ),
    ]
