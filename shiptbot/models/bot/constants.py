from shiptbot.util.const_namespace import BaseConstNamespace, as_choice


class OfferStatusNameSpace(BaseConstNamespace):
    ACCEPTED = as_choice("ac", "Accepted")
    SKIPPED = as_choice("sk", "Skipped")
    GONE = as_choice("go", "Gone")


OFFER_STATUS = OfferStatusNameSpace()


class DayNameSpace(BaseConstNamespace):
    MONDAY = as_choice("mon", "Monday")
    TUESDAY = as_choice("tue", "Tuesday")
    WEDNESDAY = as_choice("wed", "Wednesday")
    THURSDAY = as_choice("thu", "Thursday")
    FRIDAY = as_choice("fri", "Friday")
    SATURDAY = as_choice("sat", "Saturday")
    SUNDAY = as_choice("sun", "Sunday")


DAYS = DayNameSpace()


# Default values for store settings
class StoreDefaults:
    MIN_PAY = 10.00
    MIN_PAY_PER_MILE = 1.00
    MIN_DISTANCE_MILES = 5.00
    MAX_DISTANCE_MILES = 20.00
