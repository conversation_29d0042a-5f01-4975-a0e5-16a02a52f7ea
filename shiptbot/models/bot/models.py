import pydash
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.db import models

from shiptbot.util.buids import BUIDCode
from shiptbot.util.models import BasestModel

from .constants import DAYS, OFFER_STATUS, StoreDefaults


class BotSetting(BasestModel):
    BUID_OBJ_CODE = BUIDCode.SETTING

    user = models.OneToOneField("user.User", on_delete=models.CASCADE)
    refresh_rate_seconds = models.IntegerField(
        default=2, validators=[MinValueValidator(1)]
    )
    stop_after_accepted = models.BooleanField(default=False)
    stop_after_missed = models.BooleanField(default=False)

    notis_after_accept = models.BooleanField(default=False)
    notis_afer_missed = models.BooleanField(default=False)

    notis_via_email = models.BooleanField(default=False)
    notis_via_telegram = models.BooleanField(default=False)


class Session(BasestModel):
    BUID_OBJ_CODE = BUIDCode.SESSION

    user = models.ForeignKey("user.User", on_delete=models.CASCADE)
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ("-started_at",)

    def __str__(self):
        return f"{self.started_at}"


class Offer(BasestModel):
    BUID_OBJ_CODE = BUIDCode.OFFER

    user = models.ForeignKey("user.User", on_delete=models.CASCADE)
    session = models.ForeignKey(
        Session, on_delete=models.CASCADE, null=True, blank=True
    )
    status = models.CharField(
        max_length=2, choices=OFFER_STATUS.CHOICES, default=OFFER_STATUS.SKIPPED
    )
    offer_data = models.JSONField(default=dict, blank=True, null=False)
    skip_reason = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ("-created_at",)

    @property
    def est_shopper_pay(self):
        pay_str = pydash.get(self.offer_data, "estimated_shopper_pay")
        return float(pay_str.replace("$", ""))

    @property
    def zone_name(self):
        return pydash.get(self.offer_data, "orders[0].tracked_properties.zone_name")

    @property
    def est_shop_time(self):
        return pydash.get(self.offer_data, "orders[0].tracked_properties.est_shop_time")

    @property
    def est_drive_time(self):
        return pydash.get(
            self.offer_data, "orders[0].tracked_properties.est_drive_time"
        )

    @property
    def promo_pay(self):
        return pydash.get(self.offer_data, "promo_pay_amount")

    @property
    def order_status(self):
        return pydash.get(self.offer_data, "orders[0].tracked_properties.order_status")

    @property
    def timeslot_info(self):
        return pydash.get(self.offer_data, "orders[0].tracked_properties.timeslot_info")

    @property
    def store_name(self):
        return pydash.get(self.offer_data, "orders[0].tracked_properties.store_name")

    @property
    def store_location_name(self):
        return pydash.get(
            self.offer_data, "orders[0].tracked_properties.store_location_name"
        )


class Zone(BasestModel):
    BUID_OBJ_CODE = BUIDCode.ZONE

    user = models.ForeignKey("user.User", on_delete=models.CASCADE)
    zone_id = models.CharField(max_length=20)
    zone_name = models.CharField(max_length=100)


class Store(BasestModel):
    BUID_OBJ_CODE = BUIDCode.STORE

    user = models.ForeignKey("user.User", on_delete=models.CASCADE)
    zone = models.ForeignKey(Zone, on_delete=models.CASCADE)
    store_id = models.CharField(max_length=20)
    store_name = models.CharField(max_length=100)
    store_data = models.JSONField(default=dict, blank=True, null=False)

    enabled = models.BooleanField(default=False)
    min_pay = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=StoreDefaults.MIN_PAY,
        blank=True,
        null=True,
    )
    min_pay_per_mile = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=StoreDefaults.MIN_PAY_PER_MILE,
        blank=True,
        null=True,
    )
    min_distance_miles = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=StoreDefaults.MIN_DISTANCE_MILES,
        blank=True,
        null=True,
    )
    max_distance_miles = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=StoreDefaults.MAX_DISTANCE_MILES,
        blank=True,
        null=True,
    )

    class Meta:
        unique_together = (
            "user",
            "store_id",
        )


class Day(BasestModel):
    BUID_OBJ_CODE = BUIDCode.DAY

    user = models.ForeignKey("user.User", on_delete=models.CASCADE)
    enabled = models.BooleanField(default=True)
    day = models.CharField(max_length=3, choices=DAYS.CHOICES)

    def __str__(self):
        return self.get_day_display()

    class Meta:
        unique_together = (
            "user",
            "day",
        )


class Range(BasestModel):
    BUID_OBJ_CODE = BUIDCode.RANGE

    user = models.ForeignKey("user.User", on_delete=models.CASCADE)
    day = models.ForeignKey(Day, on_delete=models.CASCADE)
    start_time = models.TimeField()
    end_time = models.TimeField()

    overwrite_store_min_pay = models.BooleanField(default=False)
    min_pay = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    overwrite_store_distance = models.BooleanField(default=False)
    min_distance_miles = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    max_distance_miles = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    class Meta:
        unique_together = (
            "start_time",
            "end_time",
            "day",
            "user",
        )

    def __str__(self):
        return f"{self.start_time} - {self.end_time}"
