from django.contrib import admin

from shiptbot.models.appconfig.models import Setting
from shiptbot.util.forms import prettify_for_admin


@admin.register(Setting)
class SettingAdmin(admin.ModelAdmin):
    fields = (
        "kind",
        "name",
        "slug",
        "default",
        "metadata",
        "note",
    )
    readonly_fields = ("formatted_metadata",)

    list_display = ("id", "slug", "name", "default", "note")
    search_fields = ("slug", "name", "metadata")

    @prettify_for_admin("Formatted Metadata")
    def formatted_metadata(self, obj):
        return obj.metadata
