from django.core.management.base import BaseCommand

from shiptbot.models.subscription.models import Plan

stripe_price_ids = {
    "Weekly": "price_1Rl246DIKALylUjPrPBKjMzI",
    "Monthly": "price_1Rl23fDIKALylUjP4fbtuP3J",
}


class Command(BaseCommand):
    help = "Create sample subscription plans for development"

    def handle(self, *args, **options):
        self.stdout.write("Creating sample subscription plans...")

        for plan_name, stripe_price_id in stripe_price_ids.items():
            if plan_name == "Weekly":
                plan, created = Plan.objects.get_or_create(
                    stripe_price_id=stripe_price_id,
                    defaults={
                        "name": "Weekly",
                        "metadata": {
                            "name": "Weekly",
                            "items": [
                                {"label": "Unlimited Offers / Day"},
                                {"label": "Advanced Offer Filtering"},
                                {"label": "Real-time Offer Notifications"},
                                {"label": "Auto-Accept Settings"},
                                {"label": "Zone & Store Preferences"},
                                {"label": "Online Support"},
                                {"label": "Email Support"},
                                {"label": "Mobile App Access"},
                            ],
                            "price": 14,
                            "priceLabel": "per week",
                            "price_per_day": 2.0,
                            "description": "Perfect for active shoppers",
                            "priceLabel2": "Billed weekly",
                            "is_popular": False,
                        },
                    },
                )
            elif plan_name == "Monthly":
                plan, created = Plan.objects.get_or_create(
                    stripe_price_id=stripe_price_id,
                    defaults={
                        "name": "Monthly",
                        "metadata": {
                            "name": "Monthly",
                            "items": [
                                {"label": "Unlimited Offers / Day"},
                                {"label": "Advanced Offer Filtering"},
                                {"label": "Real-time Offer Notifications"},
                                {"label": "Auto-Accept Settings"},
                                {"label": "Zone & Store Preferences"},
                                {"label": "Online Support"},
                                {"label": "Email Support"},
                                {"label": "Mobile App Access"},
                            ],
                            "price": 45,
                            "priceLabel": "per month",
                            "price_per_day": 1.5,
                            "description": "Best value for serious shoppers",
                            "priceLabel2": "Save 20% compared to weekly billing",
                            "is_popular": True,
                        },
                    },
                )

            if created:
                self.stdout.write(f"✓ Created: {plan.name}")
            else:
                self.stdout.write(f"✓ Already exists: {plan.name}")

        self.stdout.write(
            self.style.SUCCESS(
                f"\nSuccessfully created/verified {Plan.objects.count()} subscription plans!"
            )
        )
