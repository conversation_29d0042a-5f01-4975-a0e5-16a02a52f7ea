import arrow
import pydash
from django.db import models

from shiptbot.services.stripe.constants import STRIPE_SUBSCRIPTION_STATUS
from shiptbot.util.buids import BUIDCode
from shiptbot.util.models import BasestModel


class Plan(BasestModel):
    name = models.CharField(max_length=100)
    metadata = models.JSONField(default=dict, blank=True, null=False)
    stripe_price_id = models.CharField(
        max_length=50, unique=True, blank=True, null=True
    )

    def __str__(self):
        return self.name


class Subscription(BasestModel):
    BUID_OBJ_CODE = BUIDCode.SUBSCRIPTION

    user = models.OneToOneField("user.User", on_delete=models.CASCADE)
    plan = models.ForeignKey(Plan, on_delete=models.SET_NULL, null=True, blank=True)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)
    stripe_subscription_id = models.Char<PERSON>ield(
        max_length=50, unique=True, blank=True, null=True
    )
    stripe_upstream_data = models.JSONField(default=dict, blank=True, null=False)

    def __str__(self):
        return f"{self.user.email} - {self.status}"

    @property
    def status(self):
        if self.end_date and arrow.utcnow() > arrow.get(self.end_date):
            return STRIPE_SUBSCRIPTION_STATUS.CANCELED
        return pydash.get(
            self.stripe_upstream_data, "status", STRIPE_SUBSCRIPTION_STATUS.ACTIVE
        )

    @property
    def is_active(self):
        return self.status == STRIPE_SUBSCRIPTION_STATUS.ACTIVE

    @property
    def period_end(self):
        # Get current_period_end from Stripe subscription items data
        period_end_timestamp = pydash.get(
            self.stripe_upstream_data, "items.data[0].current_period_end", None
        )
        if period_end_timestamp:
            return arrow.get(period_end_timestamp).datetime
        return None

    @property
    def period_start(self):
        # Get current_period_start from Stripe subscription items data
        period_start_timestamp = pydash.get(
            self.stripe_upstream_data, "items.data[0].current_period_start", None
        )
        if period_start_timestamp:
            return arrow.get(period_start_timestamp).datetime
        return None

    def get_status_display(self):
        return STRIPE_SUBSCRIPTION_STATUS.get_label(self.status)
