from django.contrib import admin

from shiptbot.models.user.models import User


# Register your models here.
@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    search_fields = (
        "id",
        "email",
    )
    readonly_fields = (
        "id",
        "created_at",
        "updated_at",
    )
    list_display = (
        "email",
        "is_active",
        "is_staff",
        "needs_initialization",
        "shipt_is_running",
    )
    list_filter = (
        "is_active",
        "is_staff",
        "needs_initialization",
        "shipt_is_running",
    )
