import logging

import timber
from celery import Task

logger = logging.getLogger(__name__)


class LoggingTask(Task):
    def _timber_task_context(self):
        return timber.context(
            task={
                "id": self.request.id,
                "name": self.name,
                "retries": self.request.retries,
            }
        )

    def __call__(self, *args, **kwargs):
        with self._timber_task_context():
            return super().__call__(*args, **kwargs)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        with self._timber_task_context():
            logger.warning(
                "Async task {} failed".format(task_id),
                exc_info=exc,
                extra={
                    "async_task_failed": {
                        "occurred": True,
                        "retries": self.request.retries,
                        "kwargs": kwargs,
                        "args": args,
                    }
                },
            )

    def on_success(self, retval, task_id, args, kwargs):
        with self._timber_task_context():
            logger.info(
                "Async task {} succeeded".format(task_id),
                extra={
                    "async_task_succeeded": {
                        "occurred": True,
                        "retries": self.request.retries,
                        "kwargs": kwargs,
                        "args": args,
                    }
                },
            )
