from rest_framework import serializers

from shiptbot.models.user.models import User


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model, for auth purposes"""

    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "first_name",
            "last_name",
            "is_active",
            "shipt_logged_in",
            "shipt_is_running",
            "latest_search_session_id",
        )
        read_only_fields = (
            "id",
            "email",
            "first_name",
            "last_name",
            "is_active",
            "shipt_logged_in",
            "shipt_is_running",
            "latest_search_session_id",
        )

    latest_search_session_id = serializers.PrimaryKeyRelatedField(
        source="get_latest_search_session", read_only=True
    )

    def create(self, validated_data):
        raise NotImplementedError("Cannot create")

    def update(self, instance, validated_data):
        raise NotImplementedError("Cannot update")
