import logging

import arrow
from rest_framework import viewsets

from shiptbot.models.account.models import Account
from shiptbot.models.account.serializers import AccountSerializer
from shiptbot.services.permissions.helpers import Perms

logger = logging.getLogger(__name__)


class AccountViewSet(viewsets.ModelViewSet):
    """A viewset for viewing and editing Account instances."""

    serializer_class = AccountSerializer

    def get_queryset(self):
        return Perms.m(Account).query_all(self.request.user)
