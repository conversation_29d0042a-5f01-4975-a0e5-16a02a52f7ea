import logging

import arrow
import jwt
import pydash
from django.conf import settings
from stytch import Client

from shiptbot.models.user.models import User
from shiptbot.util.cache import get_default_cache

from .constants import CACHE_KEYS

logger = logging.getLogger(__name__)


class StytchAuthentication:
    ISSUER = settings.STYTCH_JWT_ISSUER
    AUDIENCE = settings.STYTCH_PROJECT_ID
    AUTH_HEADER_PREFIX = settings.STYTCH_AUTH_HEADER_PREFIX

    def __init__(self):
        self.client = Client(
            project_id=settings.STYTCH_PROJECT_ID, secret=settings.STYTCH_SECRET
        )

    @property
    def ready(self):
        return (
            settings.STYTCH_PROJECT_ID
            and settings.STYTCH_SECRET
            and settings.STYTCH_JWT_ISSUER
        )

    def get_member_from_bytes(self, request_auth):
        jwt_token = self.jwt_decode(request_auth.decode("utf-8"))

        return self.get_attr_from_payload(jwt_token, "sub")

    def get_attr_from_payload(self, payload, attr):
        return payload.get(attr)

    def get_auth_username_from_payload(self, payload):
        return payload.get("sub")

    def jwt_decode(self, token):
        header = jwt.get_unverified_header(token)

        cache = get_default_cache()
        jwks = cache.get(CACHE_KEYS.STYTCH_JWKS_PATTERN)

        if not jwks:
            jwks = self.get_jwks()
            cache.set(
                CACHE_KEYS.STYTCH_JWKS_PATTERN,
                jwks,
                timeout=settings.STYTCH_CACHE_TIMEOUT_SECS,
            )

        public_key = None
        for jwk in jwks.keys:
            if jwk.kid == header["kid"]:
                public_key = jwt.algorithms.RSAAlgorithm.from_jwk(jwk.json())

        if public_key is None:
            # logger.warning("Matching public key not found in Stytch JWKS")
            raise ValueError("Public key not found.")

        return jwt.decode(
            token,
            public_key,
            audience=self.AUDIENCE,
            issuer=self.ISSUER,
            algorithms=["RS256"],
        )

    def get_jwks(self):
        resp = self.client.sessions.get_jwks(project_id=settings.STYTCH_PROJECT_ID)
        return resp

    def get_user_from_auth_username(self, auth_username):
        return User.objects.get(stytch_user_id=auth_username)

    def _get_stytch_user(self, stytch_user_id):
        resp = self.client.users.get(user_id=stytch_user_id)
        if resp.status_code != 200:
            raise ValueError("Failed to retrieve Stytch user information")

        return resp

    def _create_stytch_user(self, user: User):
        resp = self.client.users.create(
            email=user.email,
            external_id=user.id,
        )

        if resp.status_code != 200:
            raise ValueError("Failed to create Auth User")

        user.stytch_user_id = resp.user.user_id
        user.save()

        return resp.user

    def create_user_from_auth_username(self, auth_username):
        stytch_user = self._get_stytch_user(auth_username)
        email = pydash.get(stytch_user, "emails[0].email")
        first_name = pydash.get(stytch_user, "name.first_name")
        last_name = pydash.get(stytch_user, "name.last_name")

        user = User.objects.create(
            email=email,
            stytch_user_id=auth_username,
            first_name=first_name,
            last_name=last_name,
            username=email,
        )

        return user


stytch_auth = StytchAuthentication()
