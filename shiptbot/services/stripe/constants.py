from shiptbot.util.const_namespace import BaseConstNamespace, as_choice


class StripeSubscriptionStatusNameSpace(BaseConstNamespace):
    INCOMPLETE = as_choice("incomplete", "Incomplete")
    INCOMPLETE_EXPIRED = as_choice("incomplete_expired", "Expired")
    PAST_DUE = as_choice("past_due", "Past Due")
    TRIALING = as_choice("trialing", "Trialing")
    ACTIVE = as_choice("active", "Active")
    CANCELED = as_choice("canceled", "Canceled")
    UNPAID = as_choice("unpaid", "Unpaid")
    PAUSED = as_choice("paused", "Paused")


STRIPE_SUBSCRIPTION_STATUS = StripeSubscriptionStatusNameSpace()
