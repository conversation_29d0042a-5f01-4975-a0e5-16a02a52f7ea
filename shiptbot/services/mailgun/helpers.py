import logging
import os
from pathlib import Path

from django.conf import settings
from mailgun.client import Client

logger = logging.getLogger(__name__)

client: Client = Client(auth=("api", settings.MAILGUN_API_KEY))


def format_dict_as_string(data: dict) -> str:
    parts = []
    for key, value in data.items():
        # Escape double quotes in string values
        if isinstance(value, str):
            value = value.replace('"', '\\"')
        parts.append(f'"{key}": "{value}"')
    return "{" + ", ".join(parts) + "}"


def send_email(to_email, subject, template, variables: dict) -> None:
    data = {
        "from": settings.MAILGUN_FROM_EMAIL,
        "to": to_email,
        "subject": subject,
        "template": template,
        "h:X-Mailgun-Variables": format_dict_as_string(variables),
    }

    req = client.messages.create(data=data, domain=settings.MAILGUN_DOMAIN)

    if req.status_code != 200:
        logger.error(
            f"Failed to send email. Status code: {req.status_code}",
            extra=dict(response=req.text),
        )


class EmailHelper:
    OFFER_TEMPLATE = "Offer"

    def __init__(self, user):
        self.user = user

    def send_offer_accepted_email(self):
        subject = "OnlyShipster has grabbed you a new Offer!"
        title = "You got an offer!"
        send_email(self.user.email, subject, self.OFFER_TEMPLATE, {"title": title})
