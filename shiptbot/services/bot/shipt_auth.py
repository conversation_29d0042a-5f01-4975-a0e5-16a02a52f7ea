import uuid

import arrow
import pydash
import requests

from shiptbot.services.bot.constants import AUTH_HEADERS_BASE


class ShiptAuth:
    def __init__(self, user):
        self.user = user
        self.headers = AUTH_HEADERS_BASE.copy()
        self.headers["x-shipt-geo-long"] = "-122.68234219244364"
        self.headers["x-shipt-geo-lat"] = "45.56178265826245"
        self.headers["x-shipt-geo-last-tracked"] = arrow.utcnow().isoformat()

        if not self.user.shipt_client_id:
            self.user.shipt_client_id = uuid.uuid4()
            self.user.save()

    def create_oauth_token(self, username, password):
        url = "https://api.shipt.com/auth/v3/oauth/token"

        data = {
            "grant_type": "password",
            "client_id": str(self.user.shipt_client_id),
            "username": username,
            "password": password,
        }

        response = requests.post(url, headers=self.headers, data=data)
        if response.status_code != 200:
            raise Exception(
                f"Failed to create oauth token. Status code: {response.status_code} - {response.text}"
            )

        return response.json()

    def get_mfa_channels(self, token):
        url = "https://api.shipt.com/auth/v1/oauth/mfa/channels"

        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {token}"

        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            raise Exception(
                f"Failed to get mfa channels. Status code: {response.status_code} - {response.text}"
            )

        return response.json()

    def send_mfa_code(self, token, channel_id):
        url = f"https://api.shipt.com/auth/v1/oauth/mfa/channels/{channel_id}/codes"
        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {token}"

        response = requests.post(url, headers=headers)
        if response.status_code != 201:
            raise Exception(
                f"Failed to get mfa code. Status code: {response.status_code} - {response.text}"
            )

        return response.json()

    def verify_mfa_code(self, token, channel_id, code):
        url = f"https://api.shipt.com/auth/v1/oauth/mfa/channels/{channel_id}/codes/{code}?client_id={str(self.user.shipt_client_id)}"
        headers = self.headers.copy()
        headers["authorization"] = f"Bearer {token}"

        response = requests.post(url, headers=headers)
        if response.status_code != 200:
            raise Exception(
                f"Failed to verify mfa code. Status code: {response.status_code} - {response.text}"
            )

        return response.json()

    def refresh_token(self):
        url = "https://api.shipt.com/auth/v3/oauth/token"
        data = {
            "grant_type": "refresh_token",
            "client_id": str(self.user.shipt_client_id),
            "refresh_token": self.user.shipt_refresh_token,
        }
        print(data)
        response = requests.post(url, headers=self.headers, data=data)
        if response.status_code != 200:
            raise Exception(
                f"Failed to refresh token. Status code: {response.status_code} - {response.text}"
            )

        resp_data = response.json()
        self.user.shipt_access_token = pydash.get(resp_data, "access_token")
        self.user.shipt_refresh_token = pydash.get(resp_data, "refresh_token")
        self.user.save()

        return resp_data
