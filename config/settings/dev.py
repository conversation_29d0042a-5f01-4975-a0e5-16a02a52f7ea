# -*- coding: utf-8 -*-
"""
Dev settings
"""
from .common import *  # noqa

# DEBUG
# ------------------------------------------------------------------------------
# TEMPLATES[0]["OPTIONS"]["debug"] = DEBUG

# Allowed Hosts
# ------------------------------------------------------------------------------
ALLOWED_HOSTS = env.list(
    "DJANGO_ALLOWED_HOSTS", default=[".localhost", "127.0.0.1", ".ngrok.io"]
)

# INTERNAL_IPS = (
#     "127.0.0.1",
#     "********",
# )

# Secrets
# ------------------------------------------------------------------------------
SECRET_KEY = env("DJANGO_SECRET_KEY")

# # To use whitenoise compression in dev, set this in .env_dev to:
# # 'whitenoise.storage.CompressedManifestStaticFilesStorage'
# STATICFILES_STORAGE = env(
#     "DJANGO_STATICFILES_STORAGE",
#     default="django.contrib.staticfiles.storage.StaticFilesStorage",
# )


# django-extensions
# ------------------------------------------------------------------------------
INSTALLED_APPS += []

CORS_ORIGIN_WHITELIST = env.list(
    "DJANGO_CORS_ORIGIN_WHITELIST", default=["http://localhost:8001"]
)

# Whitenoise
# ------------------------------------------------------------------------------

WHITENOISE_MIDDLEWARE = ["whitenoise.middleware.WhiteNoiseMiddleware"]

MIDDLEWARE = WHITENOISE_MIDDLEWARE + MIDDLEWARE

STATICFILES_STORAGE = env(
    "DJANGO_STATICFILES_STORAGE",
    default="whitenoise.storage.CompressedManifestStaticFilesStorage",
)
