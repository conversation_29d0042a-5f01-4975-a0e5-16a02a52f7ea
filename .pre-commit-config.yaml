# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
exclude: (signasure/static/|staticfiles/)
repos:
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v5.0.0
      hooks:
          - id: trailing-whitespace
          - id: end-of-file-fixer
          - id: check-yaml
          - id: check-added-large-files
    - repo: https://github.com/psf/black
      rev: 25.1.0
      hooks:
          - id: black
    - repo: https://github.com/PyCQA/isort
      rev: 6.0.1
      hooks:
          - id: isort

    -   repo: https://github.com/standard/standard
        rev: v17.1.2
        hooks:
            -   id: standard
                files: frontend/
                exclude: (frontend/node_modules, frontend/cypress, frontend/lib)
                verbose: true
                entry: bash -c 'standard --fix --env jest "$@" || true' --
