import { combineReducers } from '@reduxjs/toolkit'

import { CLEAR_STATE } from 'data/actions'
import authReducer from 'data/auth/reducer'
import entitiesReducer from 'data/entities/reducer'

const createRootReducer = (state, action) => {
  const appReducer = combineReducers({
    auth: authReducer,
    entities: entitiesReducer
  })

  if (action.type === CLEAR_STATE) {
    state = undefined
  }

  return appReducer(state, action)
}

export default createRootReducer
