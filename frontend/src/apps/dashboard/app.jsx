import { CssBaseline } from '@mui/material'
import { ThemeProvider } from '@mui/material/styles'
import { StytchProvider } from '@stytch/react'
import { StytchUIClient } from '@stytch/vanilla-js'
import { Provider } from 'react-redux'
import { RouterProvider } from 'react-router-dom'

import Feedback from 'modules/feedback/feedback'
import router from 'routes/router'
import themes from 'themes'

import { store } from './store'
import { stytchHelper } from 'modules/auth/helpers'

const stytch = new StytchUIClient(stytchHelper.publicToken)

const DashboardApp = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={themes(undefined)}>
        <StytchProvider stytch={stytch}>
          <Feedback>
            <CssBaseline />
            <RouterProvider router={router} />
          </Feedback>
        </StytchProvider>
      </ThemeProvider>
    </Provider>
  )
}

export default DashboardApp
