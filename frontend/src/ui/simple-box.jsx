import styled from '@emotion/styled'

import { COLORS, media } from 'constants/style'

export const SimpleBoxLayout = styled.div`
  &:before {
      content: '';
      display: block;
      background: linear-gradient(to top, ${COLORS.BLUE_DARK}, ${COLORS.BLUE_BRIGHT});
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: -1;
  }
  padding: 30px 15px;
  ${media.mediumUp`
    padding: 80px 20px;
  `}
`

export const SimpleBox = styled.div`
    width: 90%;
    max-width: ${props => props.isWide ? '900px' : '400px'};
    padding: ${props => props.fullBleed ? '1em' : '15px 30px 30px'};
    margin: 0 auto;
    border-radius: 2px;
    background: #FFF;
    overflow: hidden; /* This replaces clearFix */
`

export const SimpleBoxLogo = styled.div`
  height: 74px;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid ${COLORS.GRAY_CC};
  background: url(/static/images/blueform-logo.png) center 24% no-repeat;
  background-size: 180px auto;
`

export const SimpleBoxInner = styled.div`
  ${props => props.padded && 'padding: 1rem 2rem;'}
  h2 {
    margin: 22px 0 28px 0 !important;
    font-size: 26px !important;
    font-weight: 300 !important;
    color: ${COLORS.GRAY_99} !important;
  }
  label {
    display: none;
  }
`
