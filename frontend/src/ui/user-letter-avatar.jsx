import Avatar from '@mui/material/Avatar'

const COLORS = [
  '#038666'
]

function randomBackgroundColor (seed) {
  return COLORS[seed % COLORS.length]
}

// Function to generate the avatar properties based on the name
function stringAvatar (name) {
  // If no name is provided, use "Shopper" as default
  if ((!name || name.trim() === '')) {
    name = 'Shopper'
  }

  const nameParts = name.split(' ')
  // Get the first letter of the first name
  const firstLetter = nameParts[0] && nameParts[0].length > 1 ? nameParts[0][0].toUpperCase() : ''

  // Get the first letter of the last name (if present), otherwise fallback
  const secondLetter = nameParts.length > 1 && nameParts[1].length > 1 ? nameParts[1][0].toUpperCase() : ''

  const background = randomBackgroundColor(name.length)

  return {
    sx: {
      background,
      color: '#fff !important',
      '&:hover': {
        background: '#fff',
        color: '#038666 !important'
      }
    },
    children: `${firstLetter}${secondLetter}`,
  }
}

export default function UserLetterAvatar ({ name, email, ...otherProps }) {
  return (
    <Avatar {...otherProps} {...stringAvatar(name)} />
  )
}
