import { createTheme } from '@mui/material/styles'

// assets
import colors from 'assets/styles/_themes-vars.module.scss'

// project imports
import componentStyleOverrides from './component-style-override'
import themePalette from './palette'
import themeTypography from './typography'

/**
 * Represent theme style and structure as per Material-UI
 * @param {JsonObject} customization customization parameter object
 */

export const theme = (customization) => {
  const color = colors

  const themeOption = {
    colors: color,
    heading: color.grey900,
    paper: color.paper,
    backgroundPrimary: '#112d6b',
    backgroundDefault: color.paper,
    background: color.primaryLight,
    darkTextPrimary: color.grey700,
    darkTextSecondary: color.grey500,
    textDark: color.grey900,
    menuSelected: color.primaryMain,
    menuSelectedBack: color.primaryLight,
    divider: color.grey200,
    customization
  }

  const themeOptions = {
    direction: 'ltr',
    shape: {
      borderRadius: 4 // Change the default border-radius to 8px
    },
    palette: themePalette(themeOption),
    mixins: {
      toolbar: {
        minHeight: '48px',
        padding: '16px',
        '@media (min-width: 600px)': {
          minHeight: '48px'
        }
      }
    },
    typography: themeTypography(themeOption)
  }

  const themes = createTheme(themeOptions)
  themes.components = componentStyleOverrides(themeOption)

  return themes
}

export default theme
