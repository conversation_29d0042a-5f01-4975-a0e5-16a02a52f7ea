/**
 * Use this in data-tid (or classNames) to provide consistent hooks we want on DOM elements so we can reference
 * those by class in 3rd party apps.
 * FIXME - add invariant that enforces testClass contains only valid css selector chars
 */
export const nub = (nubClass, otherClasses = '') => {
  // Allow nubClass to be a string, or an array of strings
  const nubStrings = Array.isArray(nubClass) ? nubClass.filter(Boolean).join('-') : nubClass.replace(/\s/g, '-')

  return `nub-${nubStrings} ${otherClasses}`.trim()
}
