import { useState, useEffect } from 'react'
import {
  BottomNavigation,
  BottomNavigationAction,
  Paper,
  useTheme
} from '@mui/material'
import { useNavigate, useLocation } from 'react-router-dom'
import isEmpty from 'lodash/fp/isEmpty'

import { childrenRoutes } from 'routes/dashboard-routes'
import { URL_PATH_DASHBOARD } from 'constants'

const MobileBottomNavigation = ({ setCurrentPage }) => {
  const theme = useTheme()
  const navigate = useNavigate()
  const location = useLocation()
  const [value, setValue] = useState(0)

  // Filter routes that have titles (navigation items)
  const navigationRoutes = childrenRoutes.filter(route => !isEmpty(route.title))

  // Find current route index
  useEffect(() => {
    const currentIndex = navigationRoutes.findIndex(route => {
      const isRoot = location.pathname === '/'
      return route.path === location.pathname || (isRoot && route.path === URL_PATH_DASHBOARD)
    })
    if (currentIndex !== -1) {
      setValue(currentIndex)
    }
  }, [location.pathname, navigationRoutes])

  const handleChange = (event, newValue) => {
    setValue(newValue)
    const selectedRoute = navigationRoutes[newValue]
    if (selectedRoute) {
      setCurrentPage(selectedRoute.title)
      // eslint-disable-next-line no-undef
      localStorage.setItem('currentPage', selectedRoute.title)
      navigate(selectedRoute.path)
    }
  }

  return (
    <Paper
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: theme.zIndex.appBar,
        borderTop: `1px solid ${theme.palette.divider}`
      }}
      elevation={3}
    >
      <BottomNavigation
        value={value}
        onChange={handleChange}
        sx={{
          bgcolor: theme.palette.background.paper,
          '& .MuiBottomNavigationAction-root': {
            color: theme.palette.text.secondary,
            '&.Mui-selected': {
              color: theme.palette.primary.main
            }
          }
        }}
      >
        {navigationRoutes.map((route, index) => (
          <BottomNavigationAction
            key={route.path}
            label={route.title}
            icon={route.icon}
            sx={{
              minWidth: 'auto',
              '& .MuiBottomNavigationAction-label': {
                fontSize: '0.75rem',
                '&.Mui-selected': {
                  fontSize: '0.75rem'
                }
              }
            }}
          />
        ))}
      </BottomNavigation>
    </Paper>
  )
}

export default MobileBottomNavigation
