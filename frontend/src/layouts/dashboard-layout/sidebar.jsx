import { Box, Drawer, List, useMediaQuery, useTheme } from '@mui/material'
import isEmpty from 'lodash/fp/isEmpty'

import { childrenRoutes } from 'routes/dashboard-routes'

import NavItem from './nav-item'

const drawerWidth = 260

const Sidebar = ({ drawerOpen, drawerToggle, window, setCurrentPage }) => {
  const theme = useTheme()
  const matchUpMd = useMediaQuery(theme.breakpoints.up('md'))

  const drawer = (
    <Box sx={{ textAlign: 'center' }}>
      <List sx={{ p: '0 16px' }}>
        {childrenRoutes.filter(route => !isEmpty(route.title)).map((item) => (
          <NavItem key={item.path} item={item} setCurrentPage={setCurrentPage} />
        ))}
      </List>
    </Box>
  )

  const container = window !== undefined ? () => window.document.body : undefined

  return (
    <Box component='nav' sx={{ flexShrink: { md: 0 }, width: matchUpMd ? drawerWidth : 'auto' }} aria-label='mailbox folders'>
      <Drawer
        container={container}
        variant={matchUpMd ? 'persistent' : 'temporary'}
        anchor='left'
        open={drawerOpen}
        onClose={drawerToggle}
        sx={{
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            background: theme.palette.background.default,
            color: theme.palette.text.primary,
            borderRight: 'none',
            [theme.breakpoints.up('md')]: {
              top: '88px'
            }
          }
        }}
        ModalProps={{ keepMounted: true }}
        color='inherit'
      >
        {drawer}
      </Drawer>
    </Box>
  )
}

export default Sidebar
