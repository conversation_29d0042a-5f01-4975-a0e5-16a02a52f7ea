import {
  Avatar,
  Box,
  ButtonBase,
  Typography,
  useTheme,
  useMediaQuery
} from '@mui/material'
import MenuIcon from '@mui/icons-material/Menu'

import BlueinkVaultLogo from 'assets/images/bi.svg'

import Profile from './profile'

const Header = ({ handleLeftDrawerToggle, currentPage }) => {
  const theme = useTheme()
  const matchDownMd = useMediaQuery(theme.breakpoints.down('md'))

  return (
    <>
      <Box
        sx={{
          width: matchDownMd ? 'auto' : 228,
          display: 'flex',
          [theme.breakpoints.down('md')]: {
            width: 'auto'
          }
        }}
      >
        <Box component='span' sx={{ display: { xs: 'none', lg: 'block' }, flexGrow: 1, textAlign: 'center' }}>
          <img src={BlueinkVaultLogo} width='180px' />
        </Box>
        {!matchDownMd && (
          <ButtonBase sx={{ borderRadius: '8px', overflow: 'hidden' }}>
            <Avatar
              variant='rounded'
              sx={{
                ...theme.typography.commonAvatar,
                ...theme.typography.mediumAvatar,
                transition: 'all .2s ease-in-out',
                background: theme.palette.secondary.light,
                color: theme.palette.secondary.dark,
                '&:hover': {
                  background: theme.palette.secondary.dark,
                  color: theme.palette.secondary.light
                }
              }}
              onClick={handleLeftDrawerToggle}
              color='inherit'
            >
              <MenuIcon xs={{ fontSize: '1.3rem' }} />
            </Avatar>
          </ButtonBase>
        )}
      </Box>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {/* Logo for mobile */}
        <Box
          sx={{
            display: { xs: 'block', md: 'none' },
            ml: '1rem',
            mt: '1rem'
          }}
        >
          <img src={BlueinkVaultLogo} height='32px' alt='Logo' />
        </Box>

        {/* Title for desktop */}
        <Typography
          sx={{
            ml: { xs: 0, md: '2rem' },
            color: 'rgb(54, 65, 82)',
            fontWeight: 300,
            fontSize: '1.25rem',
            display: { xs: 'none', md: 'block' }
          }}
          variant='h3'
          component='h4'
        >
          {currentPage}
        </Typography>
      </Box>
      <Box sx={{ flexGrow: 1 }} />

      <Profile />
    </>
  )
}

export default Header
