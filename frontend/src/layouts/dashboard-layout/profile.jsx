import { useState, useRef, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import getOr from 'lodash/fp/getOr'
import { Box, Chip, ClickAwayListener, Divider, Grow, List, ListItemButton, ListItemIcon, ListItemText, Paper, Popper, Stack, useTheme, Typography } from '@mui/material'

import LogoutOutlinedIcon from '@mui/icons-material/LogoutOutlined'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'

import UserLetterAvatar from 'ui/user-letter-avatar'

import { URL_PATH_AUTH_LOGOUT } from 'constants/index'

const Profile = () => {
  const theme = useTheme()
  const navigate = useNavigate()
  const [open, setOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)

  const currentUser = useSelector((state) => state.auth.user)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }

  /**
   * anchorRef is used on different componets and specifying one type leads to other components throwing an error
   * */
  const anchorRef = useRef(null)

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return
    }
    setOpen(false)
  }

  const handleListItemClick = (event, index, route = '') => {
    setSelectedIndex(index)
    handleClose(event)

    if (route && route !== '') {
      navigate(route)
    }
  }

  const handleLogout = () => {
    navigate(URL_PATH_AUTH_LOGOUT)
  }

  const prevOpen = useRef(open)
  useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current.focus()
    }

    prevOpen.current = open
  }, [open])

  const greeting = () => {
    const currentHour = new Date().getHours()

    // Determine the time of day and return the appropriate greeting
    let greeting = ''
    if (currentHour >= 5 && currentHour < 12) {
      greeting = 'Good Morning'
    } else if (currentHour >= 12 && currentHour < 17) {
      greeting = 'Good Afternoon'
    } else if (currentHour >= 17 && currentHour < 21) {
      greeting = 'Good Evening'
    } else {
      greeting = 'Good Night'
    }

    const fullName = getFullName()
    return (
      <Box sx={{ p: 2, pb: 0 }}>
        <Stack sx={{ mb: 2 }}>
          <Stack direction='row' spacing={0.5} alignItems='center'>
            <Typography variant='h4'>{greeting}{fullName.trim() !== '' && ','}</Typography>
            <Typography component='span' variant='h4' sx={{ fontWeight: 400 }}>
              {fullName}
            </Typography>
          </Stack>
        </Stack>
        <Divider />
      </Box>
    )
  }

  const getFullName = () => {
    /* eslint-disable camelcase */
    if (!currentUser) return ''
    const { first_name, last_name } = currentUser
    return first_name + ' ' + last_name
    /* eslint-disable camelcase */
  }

  return (
    <>
      <Chip
        sx={{
          height: '48px',
          alignItems: 'center',
          borderRadius: '27px',
          transition: 'all .2s ease-in-out',
          borderColor: theme.palette.primary.main,
          backgroundColor: theme.palette.primary.light,
          '&[aria-controls="menu-list-grow"], &:hover': {
            borderColor: theme.palette.primary.main,
            background: `${theme.palette.primary.main}!important`,
            color: theme.palette.primary.light,
            '& svg': {
              stroke: theme.palette.primary.light
            }
          },
          '& .MuiChip-label': {
            lineHeight: 0
          }
        }}
        icon={
          <UserLetterAvatar
            sx={{
              ...theme.typography.mediumAvatar,
              margin: '8px 0 8px 8px !important',
              cursor: 'pointer'
            }}
            aria-controls={open ? 'menu-list-grow' : undefined}
            aria-haspopup='true'
            // eslint-disable-next-line react/no-children-prop
            name={getFullName()}
            email={getOr('', 'email')(currentUser)}
          />
        }
        label={<SettingsOutlinedIcon xs={{ fontSize: '1.5rem' }} color={theme.palette.primary.main} />}
        variant='outlined'
        ref={anchorRef}
        aria-controls={open ? 'menu-list-grow' : undefined}
        aria-haspopup='true'
        onClick={handleToggle}
        color='primary'
      />
      <Popper
        placement='bottom-end'
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 14]
              }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <Grow in={open} {...TransitionProps}>
            <Box sx={{
              transformOrigin: '0 0 0'
            }}
            >
              <Paper>
                <ClickAwayListener onClickAway={handleClose}>
                  <Box sx={{ boxShadow: theme.shadows[16] }}>
                    {greeting()}
                    <Box sx={{ p: 2, pt: 0 }}>
                      <List
                        component='nav'
                        sx={{
                          width: '100%',
                          maxWidth: 350,
                          minWidth: 300,
                          backgroundColor: theme.palette.background.paper,
                          borderRadius: '10px',
                          [theme.breakpoints.down('md')]: {
                            minWidth: '100%'
                          },
                          '& .MuiListItemButton-root': {
                            mt: 0.5
                          }
                        }}
                      >
                        <ListItemButton
                          sx={{ borderRadius: '4px' }}
                          selected={selectedIndex === 0}
                          onClick={(event) => handleListItemClick(event, 0, '#')}
                        >
                          <ListItemIcon>
                            <SettingsOutlinedIcon size='1.3rem' />
                          </ListItemIcon>
                          <ListItemText primary={<Typography variant='body2'>Account Settings</Typography>} />
                        </ListItemButton>

                        <ListItemButton
                          sx={{ borderRadius: '4px' }}
                          selected={selectedIndex === 4}
                          onClick={handleLogout}
                        >
                          <ListItemIcon>
                            <LogoutOutlinedIcon size='1.3rem' />
                          </ListItemIcon>
                          <ListItemText primary={
                            <Typography variant='body2'>
                              Logout
                            </Typography>
                          }
                          />
                        </ListItemButton>
                      </List>
                    </Box>
                  </Box>
                </ClickAwayListener>
              </Paper>
            </Box>
          </Grow>
        )}
      </Popper>
    </>
  )
}

export default Profile
