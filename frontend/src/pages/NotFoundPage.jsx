import { useNavigate } from 'react-router'

// material-ui
import HomeTwoToneIcon from '@mui/icons-material/HomeTwoTone'
import { Button, Card, CardContent, CardMedia, Grid, Typography } from '@mui/material'
import { styled } from '@mui/material/styles'

import imageBackground from 'assets/images/img-error-bg.svg'
import imageBlue from 'assets/images/img-error-blue.svg'
import imagePurple from 'assets/images/img-error-purple.svg'
import imageText from 'assets/images/img-error-text.svg'

// styles
const CardMediaWrapper = styled('div')({
  maxWidth: 720,
  margin: '0 auto',
  position: 'relative'
})

const ErrorWrapper = styled('div')({
  maxWidth: 350,
  margin: '0 auto',
  textAlign: 'center'
})

const ErrorCard = styled(Card)({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
})

const CardMediaBlock = styled('img')({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  animation: '3s bounce ease-in-out infinite'
})

const CardMediaBlue = styled('img')({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  animation: '15s wings ease-in-out infinite'
})

const CardMediaPurple = styled('img')({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  animation: '12s wings ease-in-out infinite'
})

// ==============================|| ERROR PAGE ||============================== //

const NotFoundPage = () => {
  const navigate = useNavigate()

  return (
    <ErrorCard>
      <CardContent>
        <Grid container justifyContent='center' spacing={2}>
          <Grid item xs={12}>
            <CardMediaWrapper>
              <CardMedia
                component='img'
                image={imageBackground}
                title='Slider5 image'
              />
              <CardMediaBlock src={imageText} title='Slider 1 image' />
              <CardMediaBlue src={imageBlue} title='Slider 2 image' />
              <CardMediaPurple src={imagePurple} title='Slider 3 image' />
            </CardMediaWrapper>
          </Grid>
          <Grid item xs={12}>
            <ErrorWrapper>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant='h1' component='div'>
                    Something is wrong
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant='body2'>
                    The page you are looking was moved, removed, renamed, or might never exist!{' '}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant='contained'
                    size='large'
                    onClick={() => {
                      navigate(-1)
                    }}
                  >
                    <HomeTwoToneIcon sx={{ fontSize: '1.3rem', mr: 0.75 }} /> Home
                  </Button>
                </Grid>
              </Grid>
            </ErrorWrapper>
          </Grid>
        </Grid>
      </CardContent>
    </ErrorCard>
  )
}

export default NotFoundPage
