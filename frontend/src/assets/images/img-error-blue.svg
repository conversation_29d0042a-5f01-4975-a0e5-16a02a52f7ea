<svg width='676' height='395' viewBox='0 0 676 395' fill='none' xmlns='http://www.w3.org/2000/svg'>
  <rect width='26.998' height='26.8293' transform='matrix(0.866041 -0.499972 0.866041 0.499972 361.873 290.126)' fill='#E3F2FD' />
  <rect width='24.2748' height='24.1231' transform='matrix(0.866041 -0.499972 0.866041 0.499972 364.249 291.115)' fill='#90CAF9' />
  <rect width='26.998' height='26.8293' transform='matrix(0.866041 -0.499972 0.866041 0.499972 291.67 86.4912)' fill='#E3F2FD' />
  <rect width='24.2748' height='24.1231' transform='matrix(0.866041 -0.499972 0.866041 0.499972 294.046 87.48)' fill='#90CAF9' />
  <g filter='url(#filter0_d)'>
    <path d='M370.694 211.828L365.394 208.768V215.835L365.404 215.829C365.459 216.281 365.785 216.724 366.383 217.069L417.03 246.308C418.347 247.068 420.481 247.068 421.798 246.308L468.671 219.248C469.374 218.842 469.702 218.301 469.654 217.77V210.861L464.282 213.962L418.024 187.257C416.708 186.497 414.573 186.497 413.257 187.257L370.694 211.828Z' fill='url(#paint0_linear)' />
  </g>
  <rect width='59.6284' height='63.9858' rx='5' transform='matrix(0.866041 -0.499972 0.866041 0.499972 364 208.812)' fill='#90CAF9' />
  <rect width='59.6284' height='63.9858' rx='5' transform='matrix(0.866041 -0.499972 0.866041 0.499972 364 208.812)' fill='url(#paint1_linear)' />
  <rect width='56.6816' height='60.8238' rx='5' transform='matrix(0.866041 -0.499972 0.866041 0.499972 366.645 208.761)' fill='url(#paint2_linear)' />
  <path d='M421.238 206.161C421.238 206.434 421.62 206.655 422.092 206.655L432.159 206.656C435.164 206.656 437.6 208.063 437.601 209.798C437.602 211.533 435.166 212.939 432.162 212.938L422.09 212.937C421.62 212.937 421.24 213.157 421.24 213.428L421.241 215.814C421.241 216.087 421.624 216.308 422.096 216.308L432.689 216.309C438.917 216.31 443.967 213.395 443.965 209.799C443.964 206.202 438.914 203.286 432.684 203.286L422.086 203.284C421.617 203.284 421.236 203.504 421.237 203.775L421.238 206.161Z' fill='#1E88E5' />
  <path d='M413.422 213.43C413.422 213.157 413.039 212.936 412.567 212.936L402.896 212.935C399.891 212.935 397.455 211.528 397.454 209.793C397.453 208.059 399.889 206.652 402.894 206.653L412.57 206.654C413.039 206.654 413.419 206.435 413.419 206.164L413.418 203.777C413.418 203.504 413.035 203.283 412.563 203.283L402.366 203.282C396.138 203.281 391.089 206.197 391.09 209.793C391.091 213.389 396.141 216.305 402.371 216.306L412.573 216.307C413.042 216.307 413.423 216.088 413.423 215.817L413.422 213.43Z' fill='#1E88E5' />
  <path d='M407.999 198.145L411.211 201.235C411.266 201.288 411.332 201.336 411.405 201.379C411.813 201.614 412.461 201.669 412.979 201.49C413.59 201.278 413.787 200.821 413.421 200.469L410.209 197.379C409.843 197.027 409.051 196.913 408.441 197.124C407.831 197.335 407.633 197.793 407.999 198.145Z' fill='#1E88E5' />
  <path d='M416.235 200.853C416.235 201.058 416.38 201.244 416.613 201.379C416.846 201.513 417.168 201.597 417.524 201.597C418.236 201.596 418.813 201.263 418.813 200.852L418.812 197.021C418.811 196.61 418.234 196.277 417.522 196.277C416.811 196.278 416.234 196.611 416.234 197.022L416.235 200.853Z' fill='#1E88E5' />
  <path d='M421.627 200.47C421.317 200.769 421.412 201.143 421.82 201.379C421.893 201.421 421.977 201.459 422.069 201.491C422.68 201.703 423.472 201.588 423.838 201.236L427.047 198.147C427.413 197.794 427.215 197.337 426.605 197.126C425.994 196.915 425.203 197.029 424.836 197.381L421.627 200.47Z' fill='#1E88E5' />
  <path d='M427.056 221.447L423.844 218.357C423.478 218.005 422.686 217.891 422.076 218.102C421.466 218.314 421.268 218.771 421.634 219.123L424.846 222.213C424.901 222.266 424.967 222.314 425.04 222.357C425.448 222.592 426.097 222.647 426.614 222.468C427.225 222.257 427.423 221.799 427.056 221.447Z' fill='#1E88E5' />
  <path d='M418.82 218.739C418.82 218.328 418.243 217.995 417.531 217.995C416.819 217.995 416.242 218.329 416.242 218.74L416.243 222.57C416.244 222.776 416.388 222.962 416.621 223.096C416.854 223.231 417.177 223.314 417.533 223.314C418.245 223.314 418.822 222.981 418.821 222.57L418.82 218.739Z' fill='#1E88E5' />
  <path d='M413.428 219.122C413.794 218.77 413.596 218.312 412.986 218.101C412.375 217.89 411.584 218.004 411.217 218.356L408.008 221.445C407.698 221.744 407.793 222.118 408.201 222.354C408.274 222.396 408.358 222.434 408.45 222.466C409.061 222.678 409.853 222.563 410.219 222.211L413.428 219.122Z' fill='#1E88E5' />
  <defs>
    <filter id='filter0_d' x='301.394' y='186.687' width='232.264' height='208.191' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'>
      <feFlood flood-opacity='0' result='BackgroundImageFix' />
      <feColorMatrix in='SourceAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
      <feOffset dy='84' />
      <feGaussianBlur stdDeviation='32' />
      <feColorMatrix type='matrix' values='0 0 0 0 0.129412 0 0 0 0 0.588235 0 0 0 0 0.952941 0 0 0 0.2 0' />
      <feBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow' />
      <feBlend mode='normal' in='SourceGraphic' in2='effect1_dropShadow' result='shape' />
    </filter>
    <linearGradient id='paint0_linear' x1='417.526' y1='205.789' x2='365.394' y2='216.782' gradientUnits='userSpaceOnUse'>
      <stop stop-color='#2196F3' />
      <stop offset='1' stop-color='#B1DCFF' />
    </linearGradient>
    <linearGradient id='paint1_linear' x1='0.503035' y1='2.68177' x2='20.3032' y2='42.2842' gradientUnits='userSpaceOnUse'>
      <stop stop-color='#FAFAFA' stop-opacity='0.74' />
      <stop offset='1' stop-color='#91CBFA' />
    </linearGradient>
    <linearGradient id='paint2_linear' x1='-18.5494' y1='-44.8799' x2='14.7845' y2='40.5766' gradientUnits='userSpaceOnUse'>
      <stop stop-color='#FAFAFA' stop-opacity='0.74' />
      <stop offset='1' stop-color='#91CBFA' />
    </linearGradient>
  </defs>
</svg>
