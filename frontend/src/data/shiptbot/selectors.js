import { createSelector } from 'reselect'

const getBotSettingsEntities = state => state.entities.botsettings || {}

export const getBotSettings = createSelector(
  [getBotSettingsEntities],
  (botSettingsEntities) => {
    const botSettingsArray = Object.values(botSettingsEntities)
    return botSettingsArray.length > 0 ? botSettingsArray[0] : null
  }
)

// Loading states are handled locally in components, not in Redux store
export const getBotSettingsLoading = () => false
export const getBotSettingsError = () => null
