import update from 'immutability-helper'
import { combineActions, handleActions } from 'redux-actions'

import { AUTH_STATUS } from 'constants/backend'
import { LOGOUT_REDUX_ACTION } from 'constants/storage'
import * as actions from './actions'

const initialState = {
  // token, failed are used for backend authentication, with our own JWTs
  token: '',
  authStatus: null, // on of the AUTH_STATUS constants
  user: null
}

/**
 * Login for old-JWT tokens (not Auth0)
 */
const updateLoggedInUser = (state, action) => {
  const { token, user } = action.payload
  return update(state, {
    token: { $set: token },
    authStatus: { $set: AUTH_STATUS.VALID },
    user: { $set: user }
  })
}

const authReducer = handleActions({
  [actions.authFailed]: (state, action) => {
    const { status } = action.payload
    return update(state, {
      authStatus: { $set: status || AUTH_STATUS.ERR_FAILED }
    })
  },
  [actions.authSuccess]: (state) => {
    return update(state, {
      authStatus: { $set: AUTH_STATUS.VALID }
    })
  },
  [combineActions(actions.authAsyncActions('user').success, actions.setUser)]: (state, action) => {
    const authUser = action.payload
    return update(state, {
      authStatus: { $set: AUTH_STATUS.VALID },
      user: { $set: authUser }
    })
  },
  [actions.authAsyncActions('login').success]: updateLoggedInUser,
  [actions.login]: updateLoggedInUser,
  [actions.updateAuthStatus]: (state, action) => {
    const { status } = action.payload
    return update(state, {
      authStatus: { $set: status }
    })
  },
  [actions.authAsyncActions('login').failure]: (state, action) => {
    return update(state, {
      authStatus: { $set: AUTH_STATUS.ERR_FAILED },
      token: { $set: '' },
      user: { $set: null }
    })
  },
  // This reducer function fires in response to two actions. The LOGOUT_REDUX_ACTION
  // exists so that other parts of the system can trigger a logout without having
  // to know about authAsyncActions, etc
  [combineActions(actions.authAsyncActions('logout').request, LOGOUT_REDUX_ACTION)]: (state, action) => {
    return initialState
  }
}, initialState)

export default authReducer
