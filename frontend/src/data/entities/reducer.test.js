import size from 'lodash/fp/size'

import { ENTITY_TYPES } from './constants'
import entitiesReducer, {
  docFlecksByPk,
  docFleckValuesByPk,
  rollbackifyPk,
  updateDocFlecksFromDocFields,
  updateDocFleckValues
} from './reducer'
import {
  optimisticClear,
  optimisticRollback,
  optimisticUpdate
} from './actions'

const docFleckList = [
  {
    pk: 123,
    value_ctx: {
      packet: 'pXx8uEe00'
    },
    value: {
      val_text: 'whatever'
    },
    dontcare: 'xyz'
  },
  {
    pk: 234,
    value_ctx: {
      bundle: 'byy0uEe00'
    },
    value: {
      val_date: '2018-01-19'
    },
    dontcare: 'abc'
  },
  {
    pk: 333,
    value_ctx: {
      bundle: 'byy0uEe00'
    },
    value: {
      val_text: 'Another text. Wow.'
    },
    dontcare: 'abc'
  },
  {
    pk: 350,
    value_ctx: {
      bundle: 'bxxABCDEFG'
    },
    value: {
      val_text: 'More text'
    },
    dontcare: 'abc'
  },
  {
    pk: 442,
    value: {
      val_date: '2018-01-19'
    },
    dontcare: 'abc'
  }
]

describe('docFlecksByPk', () => {
  it('should unflatten a list of DocFlecks, and remove values', () => {
    const obj = docFlecksByPk(docFleckList)
    expect(size(obj)).toEqual(5)
    expect(obj['123']).toEqual({
      pk: 123,
      dontcare: 'xyz'
    })
  })
})

describe('docFleckValuesByPk', () => {
  it('should unflatten values from DocFlecks', () => {
    const obj = docFleckValuesByPk('bundle')(docFleckList)

    expect(size(obj)).toEqual(2)
    expect(obj).toEqual({
      byy0uEe00: {
        234: { value: { val_date: '2018-01-19' } },
        333: { value: { val_text: 'Another text. Wow.' } }
      },
      bxxABCDEFG: {
        350: { value: { val_text: 'More text' } }
      }
    })
  })
})

describe('updateDocFleckOrders', () => {
  const entityState = {
    docFlecks: {
      123: {
        pk: 123,
        value_ctx: {
          packet: 'pXx8uEe00'
        },
        value: {
          val_text: 'whatever'
        },
        dontcare: 'xyz',
        order: 10,
        options: [
          { value: 'x', label: 'x' }
        ]
      },
      876: {
        pk: 876,
        value_ctx: {
          bundle: 'byy0uEe00'
        },
        value: {
          val_date: '2018-01-19'
        },
        dontcare: 'abc',
        order: 20
      }
    }
  }

  const newDocFields = {
    123: {
      order: 99,
      dontcare: 'not updated',
      options: [
        { value: 'y', label: 'y' }
      ]
    }
  }

  const extraDocFields = {
    123: {
      order: 99,
      dontcare: 'not updated'
    },
    234: {
      order: 100
    },
    888: {
      order: 101,
      dontcare: 'not added'
    }
  }

  it('should update only specified fields', () => {
    const newState = updateDocFlecksFromDocFields(entityState, newDocFields)
    expect(size(newState.docFlecks)).toEqual(2)
    expect(newState.docFlecks['123'].order).toEqual(99)
    expect(newState.docFlecks['123'].dontcare).toEqual('xyz')
    // Options are replaced, not merged
    expect(newState.docFlecks['123'].options).toEqual(newDocFields['123'].options)
  })

  it('should not add new docFlecks', () => {
    const newState = updateDocFlecksFromDocFields(entityState, extraDocFields)
    expect(size(newState.docFlecks)).toEqual(2)
    expect(newState.docFlecks['123'].order).toEqual(99)
    expect(newState.docFlecks['123'].dontcare).toEqual('xyz')
  })

  it('should replace options, not merge options', () => {
    const newState = updateDocFlecksFromDocFields(entityState, extraDocFields)
    expect(size(newState.docFlecks)).toEqual(2)
    expect(newState.docFlecks['123'].order).toEqual(99)
    expect(newState.docFlecks['123'].dontcare).toEqual('xyz')
  })
})

describe('updateDocFlecks', () => {
  it('should put values in state', () => {
    let state = {}
    state = updateDocFleckValues(state, docFleckList)

    expect(size(state)).toEqual(1)
    expect(state).toHaveProperty(ENTITY_TYPES.DOCFLECK_VALUES)
    expect(size(state[ENTITY_TYPES.DOCFLECK_VALUES].bundles)).toEqual(2)
    expect(state[ENTITY_TYPES.DOCFLECK_VALUES].bundles.byy0uEe00).toEqual({
      234: { value: { val_date: '2018-01-19' } },
      333: { value: { val_text: 'Another text. Wow.' } }
    })
  })
})

describe('optimistic updates', () => {
  const entityState = {
    [ENTITY_TYPES.DOCFLECKS]: {
      123: {
        pk: 123,
        value_ctx: {
          bundle: 'b1'
        },
        value: {
          val_text: 'whatever'
        },
        whatever: 'xyz',
        order: 10
      },
      876: {
        pk: 876,
        value_ctx: {
          bundle: 'b1'
        },
        value: {
          val_date: '2018-01-19'
        },
        whatever: 'abc',
        order: 20
      }
    }
  }

  it('optimisticUpdate should update the value and mark it _pending', () => {
    const pk = 123
    const origFleck = entityState[ENTITY_TYPES.DOCFLECKS][pk]
    const newFleckData = { ...entityState[ENTITY_TYPES.DOCFLECKS][pk], whatever: 'new-value' }

    const state = entitiesReducer(entityState, optimisticUpdate(ENTITY_TYPES.DOCFLECKS, pk, newFleckData))

    expect(state).toHaveProperty(ENTITY_TYPES.DOCFLECKS)
    expect(state[ENTITY_TYPES.DOCFLECKS][pk].whatever).toEqual('new-value')
    expect(state[ENTITY_TYPES.DOCFLECKS][pk]).toHaveProperty('_pending')
    expect(state[ENTITY_TYPES.DOCFLECKS][rollbackifyPk(pk)].whatever).toEqual(origFleck.whatever)
  })

  it('optimisticUpdate should partially update the value and mark it _pending', () => {
    const pk = 123
    const origFleck = entityState[ENTITY_TYPES.DOCFLECKS][pk]
    const partialFleckData = { whatever: 'partial-value' }

    const state = entitiesReducer(
      entityState,
      optimisticUpdate(ENTITY_TYPES.DOCFLECKS, pk, partialFleckData, true)
    )

    expect(state).toHaveProperty(ENTITY_TYPES.DOCFLECKS)
    expect(state[ENTITY_TYPES.DOCFLECKS][pk].whatever).toEqual('partial-value')
    expect(state[ENTITY_TYPES.DOCFLECKS][pk].pk).toEqual(123)
    expect(state[ENTITY_TYPES.DOCFLECKS][pk].order).toEqual(10)

    expect(state[ENTITY_TYPES.DOCFLECKS][pk]).toHaveProperty('_pending')
    expect(state[ENTITY_TYPES.DOCFLECKS][rollbackifyPk(pk)].whatever).toEqual(origFleck.whatever)
  })

  it('optimisticClear should remove the rollback entity from state', () => {
    const pk = 123
    const newFleckData = { ...entityState[ENTITY_TYPES.DOCFLECKS][pk], whatever: 'new-value' }
    const state = entitiesReducer(entityState, optimisticUpdate(ENTITY_TYPES.DOCFLECKS, pk, newFleckData))

    expect(state[ENTITY_TYPES.DOCFLECKS]).toHaveProperty(rollbackifyPk(pk))

    const state2 = entitiesReducer(state, optimisticClear(ENTITY_TYPES.DOCFLECKS, pk))
    expect(state2[ENTITY_TYPES.DOCFLECKS]).not.toHaveProperty(rollbackifyPk(pk))
  })

  it('optimisticRollback should restore entity', () => {
    const pk = 123
    const origFleck = entityState[ENTITY_TYPES.DOCFLECKS][pk]
    const newFleckData = { ...entityState[ENTITY_TYPES.DOCFLECKS][pk], whatever: 'new-value' }

    const state = entitiesReducer(entityState, optimisticUpdate(ENTITY_TYPES.DOCFLECKS, pk, newFleckData))

    expect(state).toHaveProperty(ENTITY_TYPES.DOCFLECKS)
    expect(state[ENTITY_TYPES.DOCFLECKS][pk].whatever).toEqual('new-value')
    expect(state[ENTITY_TYPES.DOCFLECKS][rollbackifyPk(pk)].whatever).toEqual(origFleck.whatever)

    // Restore entity
    const state2 = entitiesReducer(state, optimisticRollback(ENTITY_TYPES.DOCFLECKS, pk))
    expect(state2[ENTITY_TYPES.DOCFLECKS]).not.toHaveProperty(rollbackifyPk(pk))
    expect(state2[ENTITY_TYPES.DOCFLECKS][pk].whatever).toEqual('xyz')
  })

  it('optimisticUpdate should no-op when given a non-existent entity', () => {
    const nonExistentPk = 999
    const newFleckData = { ...entityState[ENTITY_TYPES.DOCFLECKS][123], pk: nonExistentPk }

    const state = entitiesReducer(entityState, optimisticUpdate(ENTITY_TYPES.DOCFLECKS, nonExistentPk, newFleckData))

    expect(state).toHaveProperty(ENTITY_TYPES.DOCFLECKS)
    expect(state[ENTITY_TYPES.DOCFLECKS][nonExistentPk]).toBeUndefined()
    expect(state[ENTITY_TYPES.DOCFLECKS][rollbackifyPk(nonExistentPk)]).toBeUndefined()
  })

  it('optimisticRollback should no-op when no rollback entity exists', () => {
    const pk = 123
    const fleck123 = entityState[ENTITY_TYPES.DOCFLECKS][pk]
    const state = entitiesReducer(entityState, optimisticRollback(ENTITY_TYPES.DOCFLECKS, 123))

    expect(state).toHaveProperty(ENTITY_TYPES.DOCFLECKS)
    expect(state[ENTITY_TYPES.DOCFLECKS][pk]).toEqual(fleck123)
    // State should be unchanged
    expect(state).toEqual(entityState)
  })
})
