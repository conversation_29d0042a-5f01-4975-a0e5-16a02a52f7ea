import { ENTITY_TYPES } from 'data/entities/constants'
import { RestResource } from 'data/helpers/rest-resource'

class StoresRestResource extends RestResource {
  get defaultOptions () {
    return {
      entities: ENTITY_TYPES.STORES,
      extraVerbs: ['refresh']
    }
  }

  refresh (options = {}) {
    return this.requestThunk({
      method: 'put',
      url: this.buildUrl('/refresh/'),
      actions: this.createActionCreators('refresh'),
      options
    })
  }
}

export default StoresRestResource
