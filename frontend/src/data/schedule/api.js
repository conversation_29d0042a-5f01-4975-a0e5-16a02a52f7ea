import { RestResource } from 'data/helpers/rest-resource'
import { ENTITY_TYPES } from 'data/entities/constants'

export class DaysRestResource extends RestResource {
  get defaultOptions () {
    return {
      entities: ENTITY_TYPES.DAYS
    }
  }
}

export class RangesRestResource extends RestResource {
  get defaultOptions () {
    return {
      entities: ENTITY_TYPES.RANGES
    }
  }
}

export class DayRangesRestResource extends RestResource {
  get defaultOptions () {
    return {
      entities: ENTITY_TYPES.DAY_RANGES
    }
  }
}
