import invariant from 'invariant'
import { createActions } from 'redux-actions'

/**
 * Create an action creator function that can be used to
 * dispatch actions associated with a Resource.
 *
 * For example:
 *
 *     let usersAsyncActions = createAsyncActionCreators('users');
 *     let getUserActionCreator = usersAsyncActions('get');
 *     let action = getUserActionCreator.request({somePayloadData: 'whatever'});
 *     // action.type == 'users/get/REQUEST';
 *     // action.payload == {somePayloadData: 'whatever'};
 *
 * @param {string} baseActionType a string that will be used as the first part of the
 *  type for every action created.
 * @returns {function(*)}
 */
export function createAsyncActionCreators (baseActionType) {
  invariant(baseActionType.match(/^[a-zA-Z]+$/), 'baseActionType should only container [a-zA-Z]+')

  // FIXME - should be memoized
  return (verb) => {
    const actionCreators = createActions({
      [baseActionType]: {
        [verb]: {
          REQUEST: undefined,
          SUCCESS: undefined,
          FAILURE: undefined
        }
      }
    })

    return actionCreators[baseActionType][verb]
  }
}
