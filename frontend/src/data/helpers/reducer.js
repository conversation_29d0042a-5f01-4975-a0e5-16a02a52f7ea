import castArray from 'lodash/fp/castArray'
import curry from 'lodash/fp/curry'
import getOr from 'lodash/fp/getOr'
import pull from 'lodash/fp/pull'
import set from 'lodash/fp/set'
import uniq from 'lodash/fp/uniq'

export const pullAtPath = curry((path, value, obj) => set(
  path,
  pull(value)(getOr([], path)(obj))
)(obj))

export const pushAtPath = curry((path, value, obj) => set(
  path,
  [...getOr([], path)(obj), value]
)(obj))

export const pushAtPathUniq = curry((path, value, obj) => set(
  path,
  uniq([...getOr([], path)(obj), value])
)(obj))

/**
 * Convert a list of objects (or a single object) into a normalized
 * hash of objects, keyed by id. Assumes each object has a pkField property.
 *
 * @param objOrList a single object representing an entity, or a list of entity objects
 * @returns An object whose properties are the id's of the passed in objects and values are the objects themselves
 */
export const flattenById = (objOrList) => {
  const flattened = {}
  const objList = castArray(objOrList)

  objList.forEach((obj) => {
    const id = obj.id
    flattened[id] = obj
  })

  return flattened
}
