import { createAsyncActionCreators } from './actions'

describe('createAsyncActionCreators', () => {
  let asyncActions

  beforeEach(() => {
    asyncActions = createAsyncActionCreators('users')
  })

  it('should create actions with correct action types', () => {
    const actionCreator = asyncActions('get').request()

    expect(actionCreator.type).toEqual('users/get/REQUEST')
    expect(asyncActions('list').success().type).toEqual('users/list/SUCCESS')
    expect(asyncActions('create').failure().type).toEqual('users/create/FAILURE')
    expect(asyncActions('theVerbDoesNotMatter').request().type).toEqual('users/theVerbDoesNotMatter/REQUEST')
  })

  it('should not create non-standard actions', () => {
    expect(() => {
      asyncActions('list').nonStandardAction()
    }).toThrow(/is not a function/)
  })

  it('should be usable as an action type in a reducer', () => {
    expect(asyncActions('list').request.toString()).toEqual('users/list/REQUEST')

    expect({
      [asyncActions('list').request]: 'someFunc'
    }).toEqual({
      'users/list/REQUEST': 'someFunc'
    })
  })
})
