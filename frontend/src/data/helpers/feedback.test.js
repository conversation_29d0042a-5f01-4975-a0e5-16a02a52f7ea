import { SubmissionError } from 'redux-form'

import { STATUS } from 'data/feedback/constants'
import {
  decorateFeedbackMethods,
  errorTests,
  FORM_HANDLER,
  normalizeErrorRoutes,
  normalizeSuccessRoutes
} from './feedback'

import { apiValidationToFormErrors } from './api-errors'

/* eslint-disable no-underscore-dangle */
describe('apiValidationToFormErrors', () => {
  it('should convert non_field_errors', () => {
    const formErrors = apiValidationToFormErrors({
      non_field_errors: 'Error Here'
    })

    expect(formErrors).toHaveProperty('_error')
    expect(formErrors._error).toEqual('Error Here')
  })

  it('should convert an array of non_field_errors', () => {
    const formErrors = apiValidationToFormErrors({
      non_field_errors: [
        'First Error.',
        'Second Error.'
      ]
    })

    expect(formErrors).toHaveProperty('_error')
    expect(formErrors._error).toEqual('First Error. Second Error.')
  })

  it('should convert array for field errors', () => {
    const formErrors = apiValidationToFormErrors({
      non_field_errors: [
        'First Error.',
        'Second Error.'
      ],
      username: [
        'Required.',
        'Must be 4 characters.'
      ],
      other: [
        'Error! One!',
        'Two:',
        'Three?',
        'Four.'
      ],
      name: 'A non-array message.'
    })

    expect(formErrors).toHaveProperty('_error')
    expect(formErrors._error).toEqual('First Error. Second Error.')
    expect(formErrors).toHaveProperty('username')
    expect(formErrors.username).toEqual('Required. Must be 4 characters.')
    expect(formErrors).toHaveProperty('other')
    expect(formErrors.other).toEqual('Error! One. Two. Three. Four.')
    expect(formErrors).toHaveProperty('name')
    expect(formErrors.name).toEqual('A non-array message.')
  })
})
/* eslint-enable no-underscore-dangle */

describe('errorRoutes and successRoutes', () => {
  describe('normalizeSuccessRoutes', () => {
    it('handles empty routes', () => {
      expect(normalizeSuccessRoutes()).toEqual([])
      expect(normalizeSuccessRoutes([])).toEqual([])
      expect(normalizeSuccessRoutes(null)).toEqual([])
      expect(normalizeSuccessRoutes(undefined)).toEqual([])
      expect(normalizeSuccessRoutes({})).toEqual([])
    })

    it('throws on use of FORM_HANDLER handler', () => {
      expect(() => normalizeSuccessRoutes([{ handlers: [FORM_HANDLER] }]))
        .toThrow(/Cannot pass FORM_HANDLER/)
    })

    it('converts shorthand to full', () => {
      expect(normalizeSuccessRoutes({ testKey: 'test message' }))
        .toEqual([
          {
            handlers: ['testKey'],
            message: 'test message'
          }
        ])

      expect(normalizeSuccessRoutes({ testKey: ownProps => `test message ${ownProps.foo}` }, { foo: 'bar' }))
        .toEqual([
          {
            handlers: ['testKey'],
            message: 'test message bar'
          }
        ])
    })

    it('handles multiple routes', () => {
      expect(normalizeSuccessRoutes([
        {
          handlers: ['testKey', ownProps => `key${ownProps.id}`],
          message: 'test message',
          loadingMessage: ownProps => `loading ${ownProps.id}`
        },
        {
          handlers: ['testKey2'],
          message: ownProps => `message ${ownProps.foo}`
        }
      ], {
        id: 1997,
        foo: 'bar'
      })).toEqual([
        {
          handlers: ['testKey', 'key1997'],
          message: 'test message',
          loadingMessage: 'loading 1997'
        },
        {
          handlers: ['testKey2'],
          message: 'message bar'
        }
      ])
    })
  })

  describe('normalizeErrorRoutes', () => {
    it('handles empty routes', () => {
      expect(normalizeErrorRoutes()).toEqual([])
      expect(normalizeErrorRoutes([])).toEqual([])
      expect(normalizeErrorRoutes(null)).toEqual([])
      expect(normalizeErrorRoutes(undefined)).toEqual([])
      expect(normalizeErrorRoutes({})).toEqual([])
    })

    it('throws on use of multiple FORM_HANDLER handler', () => {
      expect(() => normalizeErrorRoutes([
        { handlers: [FORM_HANDLER, FORM_HANDLER] }
      ])).toThrow(/more than one FORM_HANDLER/)
    })

    it('converts shorthand to full', () => {
      expect(normalizeErrorRoutes('testKey'))
        .toEqual([
          {
            handlers: ['testKey'],
            test: errorTests.isAny
          }
        ])

      expect(normalizeErrorRoutes(ownProps => `testKey${ownProps.id}`, { id: 123 }))
        .toEqual([
          {
            handlers: ['testKey123'],
            test: errorTests.isAny
          }
        ])
    })

    it('handles multiple routes', () => {
      expect(normalizeErrorRoutes([
        { test: errorTests.is400, handlers: [FORM_HANDLER, 'someKey'] },
        { test: errorTests.is4XX, handlers: ['_bundleBuilder'] },
        { test: errorTests.isAny, handlers: ['_gen'], greedy: true }
      ], {
        id: 2018
      })).toEqual([
        { test: errorTests.is400, handlers: [FORM_HANDLER, 'someKey'] },
        { test: errorTests.is4XX, handlers: ['_bundleBuilder'] },
        { test: errorTests.isAny, handlers: ['_gen'], greedy: true }
      ])
    })
  })
})

describe('decorateFeedbackMethods', () => {
  let feedbackActions
  let fakeDispatch

  beforeEach(() => {
    fakeDispatch = jest.fn()
    feedbackActions = {
      add: jest.fn(),
      clear: jest.fn(),
      start: jest.fn(),
      stop: jest.fn()
    }
  })

  it('throws error on bad property method name', () => {
    const mapDispatchToProps = (dispatch, ownProps) => ({
      onSubmit: () => {},
      onSomeOtherFunc: () => {}
    })

    const wrappedMapDispatchToProps = decorateFeedbackMethods(mapDispatchToProps, {
      badMethodName: {}
    })

    expect(() => {
      wrappedMapDispatchToProps(() => undefined, () => ({}))
    }).toThrow(/Expected method property 'badMethodName' does not exist/)
  })

  it('fails if wrapped method does not return promise', () => {
    const mapDispatchToProps = (dispatch, ownProps) => ({
      onSubmit: () => 'this is a string, not a promise'
    })

    const wrappedMapDispatchToProps = decorateFeedbackMethods(mapDispatchToProps, {
      onSubmit: {}
    })

    const props = wrappedMapDispatchToProps(fakeDispatch, () => ({}))

    expect(() => {
      props.onSubmit('some args')
    }).toThrow(/to return a promise/)
  })

  it('invokes the original method to be called', () => {
    const origOnSubmit = jest.fn()

    const mapDispatchToProps = (dispatch, ownProps) => ({
      onSubmit: () => {
        origOnSubmit('I got called')
        return Promise.resolve('yay')
      }
    })

    const wrappedMapDispatchToProps = decorateFeedbackMethods(mapDispatchToProps, {
      onSubmit: {}
    })

    const props = wrappedMapDispatchToProps(fakeDispatch, () => ({}))

    props.onSubmit('some args')
    expect(origOnSubmit).toHaveBeenCalled()
  })

  it('dispatches action creators in successful async call', () => {
    expect.assertions(5)

    const origOnSubmit = jest.fn()

    const mapDispatchToProps = (dispatch, ownProps) => ({
      onSubmit: () => {
        origOnSubmit('I got called')
        return Promise.resolve('No errors here')
      }
    })

    const wrappedMapDispatchToProps = decorateFeedbackMethods(mapDispatchToProps, {
      onSubmit: {
        successRoutes: {
          successKey: 'Success Message!'
        }
      }
    }, {
      feedbackActions
    })

    const props = wrappedMapDispatchToProps(fakeDispatch, () => ({}))

    // we have to return the promise for jest to handle the test correctly
    return props.onSubmit('some args')
      .then(() => {
        expect(origOnSubmit).toHaveBeenCalledTimes(1)
        expect(feedbackActions.start).toHaveBeenCalledTimes(1)
        expect(feedbackActions.add).toHaveBeenCalledTimes(1)
        expect(feedbackActions.add).toHaveBeenCalledWith('successKey', 'Success Message!', STATUS.POSITIVE)
        expect(feedbackActions.stop).toHaveBeenCalledTimes(1)
      })
  })

  it('handles multiple success routes', () => {
    expect.assertions(5)

    const mapDispatchToProps = (dispatch, ownProps) => ({
      onSubmit: () => Promise.resolve('No errors')
    })

    const wrappedMapDispatchToProps = decorateFeedbackMethods(mapDispatchToProps, {
      onSubmit: {
        successRoutes: [
          {
            handlers: ['success1', '_success2'],
            message: ownProps => `Message ${ownProps.id}`
          },
          {
            handlers: [ownProps => `success${ownProps.id}`, '_success3'],
            message: 'hello',
            loadingMessage: ownProps => `Loading ${ownProps.id}`
          }
        ]
      }
    }, {
      feedbackActions
    })

    const props = wrappedMapDispatchToProps(fakeDispatch, { id: 1997 })

    // we have to return the promise for jest to handle the test correctly
    return props.onSubmit('some args')
      .then(() => {
        expect(feedbackActions.start).toHaveBeenCalledTimes(4)
        expect(feedbackActions.add).toHaveBeenCalledTimes(4)
        expect(feedbackActions.add).toHaveBeenCalledWith('success1', 'Message 1997', STATUS.POSITIVE)
        expect(feedbackActions.add).toHaveBeenCalledWith('success1997', 'hello', STATUS.POSITIVE)
        expect(feedbackActions.stop).toHaveBeenCalledTimes(4)
      })
  })

  it('handles errors in failing async call', () => {
    expect.assertions(10)

    const origOnSubmit = jest.fn()

    const mapDispatchToProps = (dispatch, ownProps) => ({
      onSubmit: () => {
        origOnSubmit('I got called')
        return Promise.reject({
          response: {
            status: 400,
            data: {
              non_field_errors: 'Bad stuff',
              username: ['Invalid username', 'Too short']
            }
          }
        })
      }
    })

    const wrappedMapDispatchToProps = decorateFeedbackMethods(mapDispatchToProps, {
      onSubmit: {
        errorRoutes: [
          // Route 400 errors to form and to feedback atom 'someKey'
          { test: errorTests.is400, handlers: [FORM_HANDLER, 'someKey', ownProps => `errKey${ownProps.id}`] },

          // Will not be called if the previous test caught the error
          { test: errorTests.is4XX, handlers: ['_bundleBuilder'] },

          // always route error to general
          { test: errorTests.isAny, handlers: ['_gen'], greedy: true }
        ],
        successRoutes: {
          successKey: ownProps => `Object ${ownProps.id} saved!`
        }
      }
    }, {
      feedbackActions
    })

    const props = wrappedMapDispatchToProps(fakeDispatch, { id: 2018 })

    // we have to return the promise for jest to handle the test correctly
    return props.onSubmit('some args')
      .catch((error) => {
        expect(feedbackActions.start).toHaveBeenCalledTimes(1)
        expect(feedbackActions.start).toHaveBeenCalledWith('successKey', undefined)
        expect(feedbackActions.stop).toHaveBeenCalledTimes(1)
        expect(feedbackActions.stop).toHaveBeenCalledWith('successKey', undefined)

        // 'add' was called for someKey and errKey2018, skipped for _bundleBuilder, and then called for _gen
        expect(feedbackActions.add.mock.calls[0][0]).toEqual('someKey')
        expect(feedbackActions.add.mock.calls[2][0]).toEqual('_gen')

        expect(feedbackActions.add).toHaveBeenCalledWith(
          'errKey2018',
          'Bad stuff\nusername: Invalid username. Too short',
          STATUS.NEGATIVE
        )

        expect(error).toBeInstanceOf(SubmissionError)
        expect(error.errors._error).toEqual('Bad stuff') // eslint-disable-line no-underscore-dangle
        expect(error.errors.username).toEqual('Invalid username. Too short')
      })
  })
})

const confAlt = {
  errorRoutes: [
    { test: errorTests.is400, handlers: [FORM_HANDLER] },
    { handlers: ['_gen'] }
  ],

  successRoutes: [
    {
      handlers: ['fbKey1'],
      message: ownProps => `Saved object ${ownProps.id}`,
      loadingMessage: ownProps => `Saving object ${ownProps.id}`,
      timeout: 200
    },
    {
      handlers: [ownProps => `success${ownProps.id}`],
      message: 'Saved the thing',
      timeout: 200
    }
  ]
}

const confShort = {
  errorRoutes: FORM_HANDLER,
  successRoutes: { fbKey: 'Object Saved!' }
}
