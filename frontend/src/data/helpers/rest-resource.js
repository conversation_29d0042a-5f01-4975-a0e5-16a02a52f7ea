import castArray from 'lodash/fp/castArray'
import getOr from 'lodash/fp/getOr'
import has from 'lodash/fp/has'
import mergeWith from 'lodash/fp/mergeWith'
import omit from 'lodash/fp/omit'
import reduce from 'lodash/fp/reduce'
import _ from 'lodash'

import { snackActions } from 'utils/snack-bar'
import * as entityActions from 'data/entities/actions'
import { RestAuthHelper } from 'data/helpers/rest'

import { concatIfArray } from '.'

/**
 * Make REST requests to endpoints representing Resources (aka models or entities).
 *
 * Implements verbs for the common CRUD and list operations: create, request, update, delete, list
 */
export class RestResource extends RestAuthHelper {
  list (params = {}, options = {}) {
    return this.requestThunk({
      method: 'get',
      url: this.buildUrl('/'),
      actions: this.createActionCreators('list'),
      params,
      options
    })
  }

  create (data, options = {}) {
    return this.requestThunk({
      method: 'post',
      url: this.buildUrl('/'),
      actions: this.createActionCreators('create'),
      data,
      options
    })
  }

  retrieve (id, params = {}, options = {}) {
    return this.requestThunk({
      method: 'get',
      url: this.buildUrl(`/${id}/`),
      actions: this.createActionCreators('retrieve'),
      params,
      options
    })
  }

  update (id, data, options = {}) {
    return this.requestThunk({
      method: options.partial ? 'patch' : 'put',
      url: this.buildUrl(`/${id}/`),
      data,
      actions: this.createActionCreators('update'),
      options
    })
  }

  delete (id, options = {}) {
    return this.requestThunk({
      method: 'delete',
      url: this.buildUrl(`/${id}/`),
      actions: this.createActionCreators('delete'),
      options: {
        ...options,
        toDelete: id
      }
    })
  }

  handleFailure (error, actions, dispatch, options = {}) {
    dispatch(actions.failure(error))

    this.handleAuthErrors(error, dispatch)

    if (options.onRestFailure) {
      options.onRestFailure(error, dispatch, actions)
    }

    const errorMessage = getOr('Uncaught Error', 'response.data.error.message')(error)
    if (errorMessage) {
      snackActions.error(errorMessage)
    }

    throw error
  }

  handleRequest (data, actions, dispatch, options = {}) {
    dispatch(actions.request())

    if (options.onRestRequest) {
      options.onRestRequest(data, dispatch, actions)
    }
  }

  handleSuccess (response, actions, dispatch, options = {}) {
    const entity = !options.noEntities && (options.entities || this.config.entities)
    if (entity) {
      // Note that we never delete and create from same response
      if (response.status === 204 && options.toDelete) {
        dispatch(entityActions.remove(entity, options.toDelete))
      } else {
        // Handle the main entity|entities passed as response.data,
        // and also handle (optional) related entities that are embedded in each main entity like so:
        // {
        //      ... main entity fields ...
        //      related {
        //          entityTypeA: [...list of entities of type A],
        //          entityTypeB: [...list of entities of type B],
        //      }
        // }

        const entityList = castArray(response.data)

        // Remove all of the deleted instances from the list and
        //  put them in their own list
        const deletedList = _.remove(entityList, has('__deleted'))
        const entityData = { [entity]: entityList.map(omit('related')) }

        // Create an array that is all of the __deleted objects for removeAll
        const deletedEntities = reduce(
          (allDeleted, anEntity) => mergeWith(concatIfArray, allDeleted, getOr({}, '__deleted')(anEntity)),
          {}
        )(deletedList)

        // Extract the 'related' prop from each entity in entityList, and build a single object
        // where all related entities of a certain entity type are concatenated into a single array.
        // If our main entity list consisted of bundles, the resulting relatedEntities list might
        // look like:
        // {
        //      packets: [{...packet from Bundle A...}, {...packet from Bundle B...}, {...etc...}]
        //      docspecs: [{...}, {...}, {...}]
        // }
        const relatedEntities = reduce(
          (allRelated, anEntity) => {
            const relatedData = getOr({}, 'related')(anEntity)

            // Convert to Array
            Object.keys(relatedData).forEach(key => {
              if (!Array.isArray(relatedData[key])) {
                relatedData[key] = [relatedData[key]]
              }
            })
            return mergeWith(concatIfArray, allRelated, relatedData)
          },
          {}
        )(entityList)

        dispatch(entityActions.removeAll(deletedEntities)) // eslint-disable-line no-underscore-dangle
        dispatch(entityActions.update({ ...relatedEntities, ...entityData }))
      }
    }

    dispatch(actions.success(response.data))

    if (options.onRestSuccess) {
      options.onRestSuccess(response, dispatch, actions)
    }

    if (options.fullResponse) {
      return response
    }

    return response.data
  }
}

/**
 * Create methods that will perform an optimistic update of an entity
 * during a REST API request. For example, to optimistically update
 * a DocField on submit:
 *
 *      const mapDispatchToProps = function (dispatch, ownProps) {
            return {
                onSubmitDocField: data => dispatch(API.docfields.docspec(docSpecPk).update(data.pidk, data, {
                    ...makeOptimisticUpdateOpts(ENTITY_TYPES.DOCFIELDS, data.id),
                })),
            };
        };
 */
export function makeOptimisticUpdateOpts (entityType, entityId, partial = false) {
  return {
    onRestRequest: (data, dispatch, actions) => dispatch(
      entityActions.optimisticUpdate(entityType, entityId, data, partial)
    ),
    onRestFailure: (error, dispatch, actions) => dispatch(entityActions.optimisticRollback(entityType, entityId)),
    onRestSuccess: (response, dispatch, actions) => dispatch(entityActions.optimisticClear(entityType, entityId))
  }
}

export const parentIdPattern = '{parentId}'

/**
 * Sets up a RestResource that handles Parent-Child relationships on the backend
 * @class RestChildResource
 */
export class RestChildResource extends RestResource {
  /**
     *
     * @param url
     * @param actionType
     * @param {string} setter the name of a method that can be invoked to set the parent id. Enables
     *  usage like: addresses.person(12).list()
     * @param options
     */
  constructor (path, createActionCreators, setter = '', options = {}) {
    if (!path.includes(parentIdPattern)) {
      throw new Error(`The url parameter must contain Parent Id pattern ${parentIdPattern}`)
    }

    super(path, createActionCreators, options)

    this.parentIdSetter = setter
    if (setter) {
      this[setter] = this.parent
    }
  }

  buildPath (path = '') {
    if (!this.parentId) {
      throw new Error(`ParentId is required. Did you chain your call with ${this.parentIdSetter}`)
    }

    return this.basePath.replace(parentIdPattern, this.parentId) + path
  }

  parent (parentId) {
    this.parentId = parentId
    return this
  }
}

RestResource.prototype.baseOptions = {
  needsAuth: true,
  entities: false,
  verbs: [
    'list',
    'create',
    'retrieve',
    'update',
    'delete'
  ],
  extraVerbs: []
}
