import axios from 'axios'
import invariant from 'invariant'
import getOr from 'lodash/fp/getOr'
import merge from 'lodash/fp/merge'

import { AUTH_STATUS } from 'constants/backend'
import { authFailed } from 'data/auth/actions'
import { stytchHelper } from 'modules/auth/helpers'
import { snackActions } from 'utils/snack-bar'

// This module is used by the signing app and the dashboard app, so it intentionally
// has minimal dependencies, with dashboard specific stuff split into rest-resource.js

const DEBUG_API = false

export class RestClient {
  constructor (apiPath = '/api/vb') {
    this.baseUrl = getOr('http://localhost:8000', 'VITE_REACT_APP_BACKEND_URL')(import.meta.env) + apiPath
  }

  register (propName, resourceObj, baseUrl = null) {
    if (propName in this) {
      throw new Error('An existing Resource is already registered at that name')
    }

    // this[propName] = resourceObj.isDefensive ? resourceObj : createDefensiveResource(resourceObj);
    // createDefensiveResource was causing issues...
    this[propName] = resourceObj
    resourceObj.setBaseUrl(baseUrl || this.baseUrl)
  }
}

function printError (error) {
  if (import.meta.env.NODE_ENV === 'production') {
    return
  }

  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log('API ERROR: non-200 response')
    console.log(error.response.data)
    console.log(error.response.status)
    console.log(error.response.headers)
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log('API ERROR: no response')
    console.log(error.request)
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log('API ERROR: no request sent')
    console.log('Error', error.message)
  }
}

/**
 * Return a Proxie'd RestResource object that only allows
 * calls of method names that are 'verbs' configured on the
 * RestResource.
 *
 * This exists to help avoid developer mistakes, not for strong
 * Object privacy.
 *
 * @param {object} resourceObj an instance of a RestResource (or derived class)
 * @returns {Proxy} The proxied object
 */
export function createDefensiveResource (resourceObj) {
  const safeProps = new Set([
    'actions',
    'parent', // for RestChildResource default setter
    '_reactFragment'
  ])

  // Instead of trying to polyfill Proxy, we just don't use it if it is not available. We do this because:
  // 1) We are only using Proxy to prevent development errors. It's not necessary for the app to work correctly.
  // 2) Proxy is available on modern browsers, where we are doing day-to-day development.
  // 3) We could not get a Proxy polyfill (https://github.com/GoogleChrome/proxy-polyfill/) working consistently in
  //    IE11 (and probably other browsers too)

  if (window.Proxy) {
    return new Proxy(resourceObj, {
      get: function (target, property) {
        // to check if a resourceObj has already been proxied
        if (property === 'isDefensive') {
          return true
        }

        const isParentIdSetter = target.parentIdSetter && property === target.parentIdSetter
        const isSafeProp = safeProps.has(property) && property in target

        if (target.verbs.has(property) || isSafeProp || isParentIdSetter) {
          if (typeof target[property] === 'function') {
            // Bind target, so that 'this' in the invoked method is
            // the original resourceObj, not the Proxy object. Otherwise, things break because
            // calls made inside methods of resourceObj would be proxied - rendering most of them
            // inaccessible.
            return target[property].bind(target)
          }
          return target[property]
        }

        throw new ReferenceError(`Property '${property}' is inaccessible.`)
      }
    })
  }

  return resourceObj
}

export class RestHelperBase {
  /**
   *
   * @param path
   * @param {function} createActionCreators Called with a 'verb' as an argument, it returns a set of action creators
   *  for that verb. See helpers/actions::createAsyncActionCreators().
   * @param options
   */
  constructor (path, createActionCreators, options = {}) {
    this.config = { ...this.baseOptions, ...this.defaultOptions, ...options }

    this.basePath = this.normalizeBasePath(path)
    this.baseUrl = ''
    this.actions = {}

    this.verbs = new Set(this.config.verbs.concat(this.config.extraVerbs))
    this.createActionCreators = createActionCreators
  }

  /**
   * Use this method to selectively override or add options on a class when you want to preserve most of the
   * options in Class.defaults.
   *
   * @returns {object} options to merge with Class.defaults and options passed to the constructor
   */
  get defaultOptions () {
    return {}
  }

  /**
   * For any basePath besides '', return a path with a leading '/' and no trailing slash.
   * For a basePath of '', return ''
   * @param {string} basePath the URL path to normalize
   * @returns {string} the normalized path
   */
  normalizeBasePath (basePath) {
    if (basePath === '/') {
      return basePath
    }

    if (basePath === '') {
      return basePath
    }

    return `/${basePath.replace(/^\/+/, '').replace(/\/+$/, '')}`
  }

  setBaseUrl (url) {
    // We enforce that paths start with '/', so trim trailing slash on baseUrl
    this.baseUrl = url.replace(/\/+$/, '')
  }

  buildUrl (path = '') {
    return this.baseUrl + this.buildPath(path)
  }

  buildPath (path = '') {
    return this.basePath + path
  }

  handleRequest (data, actions, dispatch, options = {}) {
    dispatch(actions.request())
  }

  handleSuccess (response, actions, dispatch, options = {}) {
    dispatch(actions.success(response.data))
    return response.data
  }

  handleAuthErrors (error, dispatch) { }

  handleFailure (error, actions, dispatch) {
    dispatch(actions.failure(error))

    this.handleAuthErrors(error, dispatch)

    if (DEBUG_API) {
      printError(error)
    }

    const errorMessage = getOr('Uncaught Error', 'response.data.error.message')(error)
    if (errorMessage) {
      snackActions.error(errorMessage)
    }

    throw error
  }

  /**
   * Return an Axios promise, or raise an error.
   *
   * This exists so we can handle authentication.
   */
  makeRequest (config, axiosOptions) {
    return axios(axiosOptions)
  }

  requestThunk ({ method, url, actions, data, params, options = {} }) {
    return (dispatch, getState) => {
      const config = { ...this.config, ...options }
      const axiosOptions = {
        method,
        url
      }

      if (data) {
        // if options.transformRequestData
        //   axiosOptions.data = options.transformRequestData(requestData, getState)
        axiosOptions.data = data
      }

      if (params) {
        // if options.transformQueryParams
        //   axiosOptions.data = options.transformRequestData(requestData, getState)
        axiosOptions.params = params
      }

      this.handleRequest(data, actions, dispatch, options)
      return this.makeRequest(config, axiosOptions, getState, dispatch)
        .then((response) => this.handleSuccess(response, actions, dispatch, options))
        .catch((error) => this.handleFailure(error, actions, dispatch, options))
    }
  }
}

/**
 * Default options for the base Class. We have two-levels of option overriding (baseOptions, and classOptions)
 * to support families of Classes with common base-class level options. For example, a RestResource Class
 * with the "standard" set of CRUD verbs.
 */
RestHelperBase.prototype.baseOptions = {
  needsAuth: true,
  verbs: [],
  extraVerbs: []
}

const KNOWN_ERR_CODES = [AUTH_STATUS.ERR_FAILED, AUTH_STATUS.ERR_BAD_TOKEN, AUTH_STATUS.ERR_NO_USER]

const KNOWN_SUSPENSION_CODES = [
  AUTH_STATUS.ERR_SUSPENDED_ACCT_MAINTENANCE,
  AUTH_STATUS.ERR_SUSPENDED_ACCT_NONPAYMENT,
  AUTH_STATUS.ERR_SUSPENDED_ACCT_OTHER
]

export class RestAuthHelper extends RestHelperBase {
  /**
   * Dispatch actions for authentication errors that might need to be handled
   * elsewhere (e.g. with a login popup, or redirection to login screen)
   */
  handleAuthErrors (error, dispatch) {
    // Handle 'true' authentication errors
    if (error.response.status === 401) {
      const code = getOr(null, 'data.code')(error.response)
      if (KNOWN_ERR_CODES.includes(code)) {
        dispatch(authFailed(code))
      } else {
        dispatch(authFailed())
      }
      throw error
    }

    // Handle Account Suspensions if and only if HTTP409 with recognized suspension code
    // Otherwise don't handle it.
    if (error.response.status === 409) {
      const code = getOr(null, 'data.code')(error.response)
      if (KNOWN_SUSPENSION_CODES.includes(code)) {
        dispatch(authFailed(code))
        throw error
      }
    }
  }

  makeRequest (config, axiosOptions, getState, dispatch) {
    if (!stytchHelper.active) {
      invariant(config.needsAuth, 'Unitialized stytchClient')
    }

    return this.makeStytchRequest(config, axiosOptions, getState)
  }

  makeStytchRequest (config, axiosOptions, getState) {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    if (config.needsAuth) {
      invariant(stytchHelper !== null, 'Unitialized stytchClient')

      const opts = merge(axiosOptions, {
        headers: {
          Authorization: stytchHelper.getStytchSessionJwt(),
          'x-timezone': timezone
        }
      })

      return axios(opts)
    }

    return axios(axiosOptions)
  }
}
