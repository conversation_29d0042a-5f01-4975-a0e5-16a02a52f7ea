import { OFFER_STATUS } from 'constants/backend'
import { selectors, ENTITY_TYPES } from 'data/entities/index'

/**
 * Get all offers from the state
 * @param {Object} state - Redux state
 * @returns {Array} Array of offer objects
 */
export function getAllOffers (state) {
  return selectors.all(state, ENTITY_TYPES.OFFERS)
}

/**
 * Get offers filtered by status
 * @param {Object} state - Redux state
 * @param {string} status - Offer status ('ac' for accepted, 'ms' for missed, 'sk' for skipped)
 * @returns {Array} Array of offers with the specified status
 */
export function getOffersByStatus (state, status) {
  const offers = getAllOffers(state)
  return offers.filter(offer => offer.status === status)
}

/**
 * Get accepted offers
 * @param {Object} state - Redux state
 * @returns {Array} Array of accepted offers
 */
export function getAcceptedOffers (state) {
  return getOffersByStatus(state, OFFER_STATUS.ACCEPTED)
}

/**
 * Get missed offers
 * @param {Object} state - Redux state
 * @returns {Array} Array of missed offers
 */
export function getMissedOffers (state) {
  return getOffersByStatus(state, OFFER_STATUS.GONE)
}

/**
 * Get skipped offers
 * @param {Object} state - Redux state
 * @returns {Array} Array of skipped offers
 */
export function getSkippedOffers (state) {
  return getOffersByStatus(state, OFFER_STATUS.SKIPPED)
}

/**
 * Get open offers (new offers from API)
 * @param {Object} state - Redux state
 * @returns {Array} Array of open offers
 */
export function getOpenOffers (state) {
  return getOffersByStatus(state, 'open')
}

/**
 * Get a specific offer by ID
 * @param {Object} state - Redux state
 * @param {string|number} offerId - Offer ID
 * @returns {Object|null} Offer object or null if not found
 */
export function getOffer (state, offerId) {
  return selectors.get(state, ENTITY_TYPES.OFFERS, offerId)
}

/**
 * Get offers sorted by creation date (newest first)
 * @param {Object} state - Redux state
 * @returns {Array} Array of offers sorted by created_at desc
 */
export function getOffersSortedByDate (state) {
  const offers = getAllOffers(state)
  return offers.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
}

/**
 * Get offers filtered by multiple criteria
 * @param {Object} state - Redux state
 * @param {Object} filters - Filter criteria
 * @param {string} filters.status - Offer status
 * @param {number} filters.minDistance - Minimum distance in miles
 * @param {number} filters.maxDistance - Maximum distance in miles
 * @param {number} filters.minPayPerMile - Minimum pay per mile
 * @param {string} filters.store - Store name (partial match)
 * @returns {Array} Array of filtered offers
 */
export function getFilteredOffers (state, filters = {}) {
  let offers = getAllOffers(state)

  if (filters.status) {
    offers = offers.filter(offer => offer.status === filters.status)
  }

  if (filters.minDistance !== undefined && filters.minDistance !== null) {
    offers = offers.filter(offer =>
      offer.offer_data?.distance_miles >= filters.minDistance
    )
  }

  if (filters.maxDistance !== undefined && filters.maxDistance !== null) {
    offers = offers.filter(offer =>
      offer.offer_data?.distance_miles <= filters.maxDistance
    )
  }

  if (filters.minPayPerMile !== undefined && filters.minPayPerMile !== null) {
    offers = offers.filter(offer =>
      offer.offer_data?.pay_per_mile >= filters.minPayPerMile
    )
  }

  if (filters.store) {
    offers = offers.filter(offer =>
      offer.offer_data?.store_name?.toLowerCase().includes(filters.store.toLowerCase())
    )
  }

  return offers.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
}
