import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  IconButton
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import RemoveIcon from '@mui/icons-material/Remove'

const TimeRangeCard = ({
  range,
  dayRange,
  onAddToSchedule,
  onRemoveFromSchedule,
  isScheduled = false
}) => {
  const formatTime = (timeString) => {
    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = timeString.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'pm' : 'am'
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    return `${displayHour}${ampm}`
  }

  const getTimeRangeDisplay = () => {
    const startTime = formatTime(range.start_time)
    const endTime = formatTime(range.end_time)
    return `${startTime}-${endTime}`
  }

  const handleToggleSchedule = () => {
    if (isScheduled) {
      onRemoveFromSchedule(dayRange.id)
    } else {
      onAddToSchedule(range.id)
    }
  }

  return (
    <Card
      sx={{
        mb: 2,
        backgroundColor: 'white',
        border: '1px solid #e0e0e0',
        borderRadius: 2,
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}
    >
      <CardContent sx={{ py: 2.5, px: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <IconButton
              size='small'
              onClick={handleToggleSchedule}
              sx={{
                mr: 2,
                color: isScheduled ? '#d32f2f' : '#2e7d32',
                backgroundColor: isScheduled ? '#ffebee' : '#e8f5e9',
                '&:hover': {
                  backgroundColor: isScheduled ? '#ffcdd2' : '#c8e6c9'
                },
                width: 32,
                height: 32
              }}
            >
              {isScheduled ? <RemoveIcon fontSize='small' /> : <AddIcon fontSize='small' />}
            </IconButton>

            <Box>
              <Typography variant='h6' component='div' sx={{ fontWeight: 600, mb: 0.5 }}>
                {getTimeRangeDisplay()}
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                {isScheduled ? 'Scheduled' : 'Add to schedule'}
              </Typography>
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default TimeRangeCard
