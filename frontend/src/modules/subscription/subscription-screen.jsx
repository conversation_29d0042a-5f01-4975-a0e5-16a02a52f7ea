import React, { useState, useEffect } from 'react'

import API from 'data/api'
import { connect } from 'react-redux'
import {
  Container,
  Grid,
  Box,
  Typography,
  Alert,
  Skeleton
} from '@mui/material'

import PlanCard from './components/plan-card'
import CurrentPlanCard from './components/current-plan-card'

const PlanSkeleton = () => (
  <Box sx={{ p: 3 }}>
    <Skeleton variant='text' width='60%' height={40} sx={{ mb: 2 }} />
    <Skeleton variant='text' width='40%' height={60} sx={{ mb: 2 }} />
    <Skeleton variant='text' width='80%' height={20} sx={{ mb: 3 }} />
    {[...Array(5)].map((_, i) => (
      <Skeleton key={i} variant='text' width='90%' height={20} sx={{ mb: 1 }} />
    ))}
    <Skeleton variant='rectangular' width='100%' height={48} sx={{ mt: 3, borderRadius: 2 }} />
  </Box>
)

const SubscriptionScreen = ({ fetchPlans, fetchSubscription, checkout, openBillingPortal }) => {
  const [plans, setPlans] = useState([])
  const [currentSubscription, setCurrentSubscription] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      setError(null)
      try {
        // Load plans
        let plansResponse = []
        try {
          plansResponse = await fetchPlans()
        } catch (err) {
          console.error('Plans API failed:', err)
          plansResponse = []
        }

        // Load subscription
        let subscriptionResponse = null
        try {
          subscriptionResponse = await fetchSubscription()
        } catch (err) {
          console.error('Subscription API failed:', err)
          subscriptionResponse = null
        }

        // Handle plans response
        if (Array.isArray(plansResponse) && plansResponse.length > 0) {
          setPlans(plansResponse)
        } else {
          console.log('No plans available from API')
          setPlans([])
        }

        // Handle subscription response
        // Show card for both subscription plans (with plan_id) and free trials (without plan_id)
        if (subscriptionResponse && (subscriptionResponse.plan_id || subscriptionResponse.start_date || subscriptionResponse.end_date)) {
          setCurrentSubscription(subscriptionResponse)
        } else {
          setCurrentSubscription(null)
        }
      } catch (error) {
        console.error('Error loading subscription data:', error)
        setPlans([])
        setError('Failed to load subscription data. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const handleSelectPlan = (plan) => {
    checkout(plan.id).then(({ url }) => {
      window.location.href = url
    })
  }

  if (loading) {
    return (
      <Container maxWidth='lg' sx={{ py: 8, px: { xs: 2, sm: 3 } }}>
        <Grid
          container
          spacing={{ xs: 2, sm: 3, md: 4 }}
          sx={{
            justifyContent: 'center',
            maxWidth: 900,
            mx: 'auto',
            px: 0
          }}
        >
          {[...Array(2)].map((_, index) => (
            <Grid item xs={12} sm={6} md={6} lg={5} key={index}>
              <Box
                sx={{
                  backgroundColor: 'white',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  overflow: 'hidden'
                }}
              >
                <PlanSkeleton />
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>
    )
  }

  return (
    <Container maxWidth='md' sx={{ mb: 8, px: { xs: 2, sm: 3 } }}>
      {/* Error Alert */}
      {error && (
        <Alert
          severity='error'
          sx={{
            mb: 4,
            maxWidth: 800,
            mx: 'auto',
            borderRadius: 2,
            backgroundColor: '#ffebee',
            '& .MuiAlert-message': {
              fontSize: '1rem'
            }
          }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Current Plan Card */}
      {currentSubscription && (
        <Box sx={{ mb: 4, maxWidth: 900, mx: 'auto', px: { xs: 0, sm: 0 } }}>
          <CurrentPlanCard subscription={currentSubscription} />
        </Box>
      )}

      {/* Plans Grid - Optimized for 2 plans */}
      <Box sx={{ maxWidth: 900, mx: 'auto', px: { xs: 0, sm: 0 } }}>
        <Grid
          container
          spacing={{ xs: 2, sm: 3, md: 4 }}
          sx={{
            justifyContent: 'center'
          }}
        >
          {plans.map((plan) => {
            const isCurrentPlan = currentSubscription?.plan_id === plan.id

            return (
              <Grid item xs={12} sm={6} md={6} lg={5} key={plan.id}>
                <PlanCard
                  plan={plan}
                  onSelectPlan={handleSelectPlan}
                  isCurrentPlan={isCurrentPlan}
                />
              </Grid>
            )
          })}
        </Grid>
      </Box>

      {/* Empty State */}
      {plans.length === 0 && !loading && (
        <Box sx={{
          textAlign: 'center',
          mt: 8,
          p: 6,
          backgroundColor: '#f8f9fa',
          borderRadius: 3,
          maxWidth: 600,
          mx: 'auto'
        }}
        >
          <Typography variant='h5' color='text.secondary' gutterBottom sx={{ fontWeight: 600 }}>
            No subscription plans available
          </Typography>
          <Typography variant='body1' color='text.secondary' sx={{ mb: 3 }}>
            We're working on bringing you amazing subscription options. Please check back soon!
          </Typography>
          <Typography variant='body2' color='text.secondary'>
            Need help? Contact our support team for more information.
          </Typography>
        </Box>
      )}
    </Container>
  )
}

const mapDispatchToProps = (dispatch) => ({
  fetchPlans: () => dispatch(API.plans.list()),
  fetchSubscription: () => dispatch(API.subscription.self()),
  checkout: (planId) => dispatch(API.subscription.checkout({ plan_id: planId })),
  openBillingPortal: () => dispatch(API.subscription.billingPortal())
})

export default connect(null, mapDispatchToProps)(SubscriptionScreen)
