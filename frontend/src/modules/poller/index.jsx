import React from 'react'
import isFunction from 'lodash/fp/isFunction'

/**
 * Reusable component to poll an API endpoint. Will call onPoll
 * using exponential backoff or exact timeout intervals
 *
 * @param {boolean} isExactTimeout - If true, uses exact baseTimeoutMs without exponential backoff calculation
 */
class Poller extends React.Component {
  state = {
    tries: 0,
    timeoutID: null
  }

  componentDidMount () {
    this.poll()
  }

  componentWillUnmount () {
    const { timeoutID } = this.state

    if (timeoutID) {
      clearTimeout(timeoutID)
    }
  }

  poll = () => {
    const {
      baseTimeoutMs,
      exponentialBase,
      isExactTimeout,
      isFinished,
      jitter,
      maxRetries,
      maxTimeoutMs,
      onHalt,
      onPoll,
      pollUntilCompleted
    } = this.props
    const { timeoutID, tries } = this.state

    if (isFinished && (!isFunction(isFinished) || isFinished())) {
      if (timeoutID) {
        clearTimeout(timeoutID)
      }
    } else {
      let timeout

      if (isExactTimeout) {
        // Use exact baseTimeoutMs without exponential backoff calculation
        timeout = baseTimeoutMs
      } else {
        // Add jitter. Try 0 gets scaled [-0.85,1.15], try 1 gets [-0.9,1.1] and after that [-0.95,1.05]
        const jitterFactor = jitter ? (1 + Math.max(1, 3 - tries) * 0.1 * (Math.random() - 0.5)) : 1

        timeout = Math.floor(
          jitterFactor * Math.min((exponentialBase ** tries) * baseTimeoutMs, maxTimeoutMs)
        )
      }

      this.setState({ tries: tries + 1 })

      onPoll()

      if ((tries < maxRetries) || pollUntilCompleted) {
        const nextTimeoutID = setTimeout(this.poll, timeout)
        this.setState({
          timeoutID: nextTimeoutID
        })
      } else if (onHalt) {
        onHalt()
      }
    }
  }

  render () {
    const { children = null } = this.props
    return children
  }
}

Poller.defaultProps = {
  baseTimeoutMs: 500,
  exponentialBase: 2,
  isExactTimeout: false,
  jitter: true,
  maxRetries: 6,
  maxTimeoutMs: 30 * 1000,
  pollUntilCompleted: false
}

export default Poller
