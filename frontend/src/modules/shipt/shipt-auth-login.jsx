import { Box, Button, Container, Typography, Paper } from '@mui/material'
import { ExitToApp as LoginIcon } from '@mui/icons-material'
import { useSelector } from 'react-redux'
import { useState } from 'react'
import { getUserId } from 'data/auth/selectors'

const ShiptAuth0Login = () => {
  const auth0LoginUrl = import.meta.env.VITE_REACT_APP_AUTH0_LOGIN_URL
  const userId = useSelector(getUserId)
  const [showIframe, setShowIframe] = useState(false)
  const [iframeUrl, setIframeUrl] = useState('')

  const handleOpenAuth0Iframe = () => {
    if (auth0LoginUrl) {
      const url = new URL(auth0LoginUrl)
      if (userId) {
        url.searchParams.append('user_id', userId)
      }
      setIframeUrl(url.toString())
      setShowIframe(true)
    } else {
      console.error('Auth0 login URL not configured')
    }
  }

  return (
    <Container
      maxWidth='lg'
      sx={{
        py: { xs: 2, sm: 3 },
        px: { xs: 2, sm: 3 },
        minHeight: '80vh'
      }}
    >
      {!showIframe ? (
        // Login prompt
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh'
          }}
        >
          <Paper
            elevation={3}
            sx={{
              p: { xs: 3, sm: 4 },
              borderRadius: 2,
              maxWidth: 500,
              width: '100%',
              textAlign: 'center'
            }}
          >
            <Box sx={{ mb: 3 }}>
              <LoginIcon
                sx={{
                  fontSize: 48,
                  color: 'primary.main',
                  mb: 2
                }}
              />
              <Typography
                variant='h4'
                component='h1'
                gutterBottom
                sx={{
                  fontWeight: 600,
                  color: 'text.primary'
                }}
              >
                Shipt Authentication
              </Typography>
            </Box>

            <Box sx={{ mb: 4 }}>
              <Typography
                variant='body1'
                sx={{
                  mb: 2,
                  color: 'text.secondary',
                  lineHeight: 1.6
                }}
              >
                To continue using ShiptBot, you need to authenticate your Shipt account through the secure Auth0 system.
              </Typography>
              <Typography
                variant='body2'
                sx={{
                  color: 'text.secondary',
                  fontStyle: 'italic'
                }}
              >
                The authentication form will be displayed below after clicking the button.
              </Typography>
            </Box>

            <Button
              variant='contained'
              size='large'
              onClick={handleOpenAuth0Iframe}
              startIcon={<LoginIcon />}
              sx={{
                py: 1.5,
                px: 4,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderRadius: 2,
                textTransform: 'none',
                boxShadow: 2,
                '&:hover': {
                  boxShadow: 4,
                  transform: 'translateY(-1px)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
              disabled={!auth0LoginUrl}
            >
              Login with Shipt
            </Button>

            {!auth0LoginUrl && (
              <Typography
                variant='caption'
                sx={{
                  display: 'block',
                  mt: 2,
                  color: 'error.main'
                }}
              >
                Error: Authentication URL not configured
              </Typography>
            )}
          </Paper>
        </Box>
      ) : (
        // Iframe view
        <Box sx={{ height: '80vh' }}>
          {iframeUrl && (
            <iframe
              src={iframeUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none'
              }}
              title='Shipt Authentication'
            />
          )}
        </Box>
      )}
    </Container>
  )
}

export default ShiptAuth0Login
