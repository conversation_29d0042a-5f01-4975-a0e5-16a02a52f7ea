import React from 'react'
import { useSelector } from 'react-redux'
import OffersScreen from 'modules/offers/offers-screen.jsx'
import { getAuthUser } from 'data/auth/selectors'
import ShiptAuth0Login from './shipt-auth-login'

const ShiptDashboard = (props) => {
  const authUser = useSelector(getAuthUser)
  const shiptLoggedIn = authUser.shipt_logged_in || false

  if (!shiptLoggedIn) {
    return (
      <div>
        <ShiptAuth0Login />
      </div>
    )
  } else {
    return (
      <OffersScreen />
    )
  }
}

export default ShiptDashboard
