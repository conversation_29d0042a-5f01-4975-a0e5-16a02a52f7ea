import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { useForm } from 'react-hook-form'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControlLabel,
  Switch,
  CircularProgress,
  Grid2,
  Divider
} from '@mui/material'

import API from 'data/api'
import { selectors as botSettingsSelectors } from 'data/shiptbot'

const SearchSettings = ({ botSettings, listBotSettings, updateBotSettings }) => {
  const [loading, setLoading] = useState(false)
  const { watch, setValue, reset } = useForm({
    defaultValues: {
      refresh_rate_seconds: 2,
      stop_after_accepted: false,
      stop_after_missed: false,
      notis_after_accept: false,
      notis_afer_missed: false,
      notis_via_email: false,
      notis_via_telegram: false
    }
  })

  useEffect(() => {
    const loadBotSettings = async () => {
      setLoading(true)
      try {
        await listBotSettings()
      } catch (error) {
        console.error('Error loading bot settings:', error)
      } finally {
        setLoading(false)
      }
    }

    loadBotSettings()
  }, [listBotSettings])

  useEffect(() => {
    if (botSettings) {
      reset({
        refresh_rate_seconds: botSettings.refresh_rate_seconds || 2,
        stop_after_accepted: botSettings.stop_after_accepted || false,
        stop_after_missed: botSettings.stop_after_missed || false,
        notis_after_accept: botSettings.notis_after_accept || false,
        notis_afer_missed: botSettings.notis_afer_missed || false,
        notis_via_email: botSettings.notis_via_email || false,
        notis_via_telegram: botSettings.notis_via_telegram || false
      })
    }
  }, [botSettings, reset])

  const handleFieldChange = (fieldName, value) => {
    setValue(fieldName, value)
    const currentData = watch()
    const updatedData = { ...currentData, [fieldName]: value }

    if (botSettings?.id) {
      updateBotSettings(botSettings.id, updatedData)
    }
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Typography variant='body1' color='text.secondary' sx={{ mb: 3 }}>
        Configure your search preferences and notification settings for the bot.
      </Typography>

      <Card>
        <CardContent sx={{ p: 3 }}>
          <Grid2 container spacing={3}>
            {/* Search Settings */}
            <Grid2 size={12}>
              <Typography variant='h6' gutterBottom>
                Search Settings
              </Typography>
            </Grid2>

            <Grid2 size={12}>
              <TextField
                label='Refresh Rate (seconds)'
                type='number'
                value={watch('refresh_rate_seconds')}
                onChange={(e) => handleFieldChange('refresh_rate_seconds', parseInt(e.target.value) || 2)}
                slotProps={{
                  htmlInput: { min: 1, step: 1 }
                }}
                fullWidth
                size='small'
                helperText='How often to check for new offers (minimum 1 second)'
              />
            </Grid2>

            <Grid2 size={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={watch('stop_after_accepted')}
                    onChange={(e) => handleFieldChange('stop_after_accepted', e.target.checked)}
                  />
                  }
                label='Stop searching after accepting an offer'
              />
            </Grid2>

            <Grid2 size={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={watch('stop_after_missed')}
                    onChange={(e) => handleFieldChange('stop_after_missed', e.target.checked)}
                  />
                  }
                label='Stop searching after missing an offer'
              />
            </Grid2>

            {/* Notification Settings */}
            <Grid2 size={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant='h6' gutterBottom>
                Notification Settings
              </Typography>
            </Grid2>

            <Grid2 size={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={watch('notis_after_accept')}
                    onChange={(e) => handleFieldChange('notis_after_accept', e.target.checked)}
                  />
                  }
                label='Send notifications after accepting offers'
              />
            </Grid2>

            <Grid2 size={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={watch('notis_afer_missed')}
                    onChange={(e) => handleFieldChange('notis_afer_missed', e.target.checked)}
                  />
                  }
                label='Send notifications after missing offers'
              />
            </Grid2>

            <Grid2 size={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant='h6' gutterBottom>
                Notification Channels
              </Typography>
            </Grid2>

            <Grid2 size={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={watch('notis_via_email')}
                    onChange={(e) => handleFieldChange('notis_via_email', e.target.checked)}
                  />
                  }
                label='Email notifications'
              />
            </Grid2>

            <Grid2 size={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={watch('notis_via_telegram')}
                    onChange={(e) => handleFieldChange('notis_via_telegram', e.target.checked)}
                  />
                  }
                label='Telegram notifications'
              />
            </Grid2>

          </Grid2>
        </CardContent>
      </Card>
    </Box>
  )
}

const mapStateToProps = (state) => ({
  botSettings: botSettingsSelectors.getBotSettings(state)
})

const mapDispatchToProps = dispatch => ({
  listBotSettings: () => dispatch(API.botsettings.list()),
  updateBotSettings: (id, data) => dispatch(API.botsettings.update(id, data))
})

export default connect(mapStateToProps, mapDispatchToProps)(SearchSettings)
