import React from 'react'
import { connect } from 'react-redux'

import API from 'data/api'
import Poller from 'modules/poller'
import { update as updateEntities } from 'data/entities/actions'
import { ENTITY_TYPES } from 'data/entities/constants'

export const OffersPollerPlain = ({
  isSearching,
  onFetchOffers,
  onUpdateOffers,
  onError,
  params
}) => (
  <>
    {isSearching && (
      <Poller
        isExactTimeout
        baseTimeoutMs={5000}
        isFinished={() => !isSearching}
        key='offers-poller'
        maxRetries={1000}
        maxTimeoutMs={30 * 1000}
        pollUntilCompleted
        onPoll={async () => {
          try {
            const response = await onFetchOffers(params)

            // Check if we have new offers data
            if (response && response.data) {
              // Transform open metro offers to our offer entity format
              const offers = response.data.offers || []
              const transformedOffers = offers.map(offer => ({
                id: offer.id || offer.bundle_id,
                status: 'ac', // New offers are accepted by default
                offer_data: offer,
                created_at: new Date().toISOString()
              }))

              if (transformedOffers.length > 0) {
                // Update entities with new offers
                onUpdateOffers(transformedOffers)
              }
            }
          } catch (error) {
            console.error('Error polling offers:', error)
            if (onError) {
              onError(error)
            }
          }
        }}
        onHalt={() => {
          console.log('Offers polling halted')
        }}
      />
    )}
  </>
)

function mapDispatchToProps (dispatch) {
  return {
    onFetchOffers: (params) => dispatch(API.offers.list(params)),
    onUpdateOffers: (offers) => dispatch(updateEntities({ [ENTITY_TYPES.OFFERS]: offers }))
  }
}

export default connect(null, mapDispatchToProps)(OffersPollerPlain)
