import React, { useState } from 'react'
import {
  Box,
  Typography,
  Pagination,
  CircularProgress,
  Paper
} from '@mui/material'
import { styled } from '@mui/material/styles'
import InboxIcon from '@mui/icons-material/Inbox'
import SearchOffIcon from '@mui/icons-material/SearchOff'
import OfferCard from './offer-card'

const EmptyStateContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  textAlign: 'center',
  backgroundColor: '#fafafa',
  border: '2px dashed #e0e0e0',
  borderRadius: 12
}))

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(4),
  gap: theme.spacing(2)
}))

const OffersList = ({
  offers = [],
  isLoading = false,
  hasFilters = false,
  itemsPerPage = 10
}) => {
  const [currentPage, setCurrentPage] = useState(1)

  // Calculate pagination
  const totalPages = Math.ceil(offers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentOffers = offers.slice(startIndex, endIndex)

  const handlePageChange = (event, page) => {
    setCurrentPage(page)
    // Scroll to top of list
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Reset to page 1 when offers change
  React.useEffect(() => {
    setCurrentPage(1)
  }, [offers])

  // Loading state
  if (isLoading) {
    return (
      <LoadingContainer>
        <CircularProgress size={40} />
        <Typography variant='body1' color='text.secondary'>
          Loading offers...
        </Typography>
      </LoadingContainer>
    )
  }

  // Empty state
  if (offers.length === 0) {
    return (
      <EmptyStateContainer elevation={0}>
        {hasFilters
          ? (
            <>
              <SearchOffIcon sx={{ fontSize: 64, color: '#bdbdbd', mb: 2 }} />
              <Typography variant='h6' color='text.secondary' gutterBottom>
                No offers found
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                Try adjusting your filters to see more results.
              </Typography>
            </>
            )
          : (
            <>
              <InboxIcon sx={{ fontSize: 64, color: '#bdbdbd', mb: 2 }} />
              <Typography variant='h6' color='text.secondary' gutterBottom>
                No offers yet
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                Offers will appear here once they are available.
              </Typography>
            </>
            )}
      </EmptyStateContainer>
    )
  }

  return (
    <Box>
      {/* Offers list */}
      <Box sx={{ mb: 3 }}>
        {currentOffers.map((offer) => (
          <OfferCard key={offer.id} offer={offer} />
        ))}
      </Box>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            color='primary'
            size='large'
            showFirstButton
            showLastButton
            sx={{
              '& .MuiPaginationItem-root': {
                fontSize: '0.875rem'
              },
              '& .Mui-selected': {
                backgroundColor: '#2e7d32 !important',
                color: 'white'
              }
            }}
          />
        </Box>
      )}
    </Box>
  )
}

export default OffersList
