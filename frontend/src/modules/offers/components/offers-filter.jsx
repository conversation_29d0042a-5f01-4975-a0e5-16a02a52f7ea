import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Grid2,
  Box,
  CircularProgress,
  Autocomplete,
  TextField,
  Button,
  Collapse
} from '@mui/material'
import { TextFieldElement, useForm } from 'react-hook-form-mui'
import { connect } from 'react-redux'
import ClearIcon from '@mui/icons-material/Clear'
import SearchIcon from '@mui/icons-material/Search'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import FilterListIcon from '@mui/icons-material/FilterList'
import { selectors as storeSelectors } from 'data/stores'

const OffersFilter = ({
  onFilterChange,
  isLoading = false,
  stores = [],
  initialFilters = {}
}) => {
  const { control, handleSubmit, watch, reset, setValue } = useForm({
    defaultValues: {
      min_distance_miles: initialFilters.min_distance_miles || '',
      max_distance_miles: initialFilters.max_distance_miles || '',
      min_pay_per_mile: initialFilters.min_pay_per_mile || '',
      store: initialFilters.store || null
    }
  })

  const [selectedStore, setSelectedStore] = useState(initialFilters.store || null)
  const [isExpanded, setIsExpanded] = useState(false)

  // Watch all form values
  const watchedValues = watch()

  // Create store options for autocomplete
  const storeOptions = stores.map(store => ({
    label: store.store_name,
    value: store.store_name,
    id: store.id
  }))

  const onSubmit = (data) => {
    const filters = {
      min_distance_miles: data.min_distance_miles ? parseFloat(data.min_distance_miles) : null,
      max_distance_miles: data.max_distance_miles ? parseFloat(data.max_distance_miles) : null,
      min_pay_per_mile: data.min_pay_per_mile ? parseFloat(data.min_pay_per_mile) : null,
      store: selectedStore?.value || null
    }

    // Remove null/empty values
    const cleanFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== null && value !== '')
    )

    onFilterChange(cleanFilters)
  }

  const handleClearFilters = () => {
    reset({
      min_distance_miles: '',
      max_distance_miles: '',
      min_pay_per_mile: '',
      store: null
    })
    setSelectedStore(null)
    onFilterChange({})
  }

  const handleStoreChange = (event, newValue) => {
    setSelectedStore(newValue)
    setValue('store', newValue)
  }

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded)
  }

  // Auto-submit when values change (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      handleSubmit(onSubmit)()
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [watchedValues, selectedStore])

  return (
    <Card sx={{
      mb: 3,
      borderRadius: { xs: 2, sm: 3 },
      boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
    }}
    >
      <CardContent sx={{
        p: { xs: 2, sm: 3 },
        '&:last-child': {
          pb: isExpanded ? { xs: 2, sm: 3 } : { xs: 2, sm: 3 }
        }
      }}
      >
        <Box
          onClick={handleToggleExpand}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: isExpanded ? { xs: 2, sm: 3 } : 0,
            cursor: 'pointer',
            py: isExpanded ? 0 : { xs: 0.5, sm: 0 }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterListIcon sx={{
              fontSize: { xs: '1.1rem', sm: '1.3rem' },
              color: 'text.secondary'
            }}
            />
            <Typography
              variant='h6'
              component='h2'
              sx={{
                fontSize: { xs: '1rem', sm: '1.1rem' },
                fontWeight: 500
              }}
            >
              Filter Offers
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {isLoading && (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
              >
                <CircularProgress size={16} />
                <Typography
                  variant='body2'
                  color='text.secondary'
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.8rem' } }}
                >
                  Searching...
                </Typography>
              </Box>
            )}

            <Box sx={{
              transition: 'transform 0.2s ease-in-out',
              transform: isExpanded ? 'rotate(-90deg)' : 'rotate(0deg)',
              display: 'flex',
              alignItems: 'center'
            }}
            >
              <ChevronLeftIcon sx={{
                fontSize: { xs: '1.2rem', sm: '1.4rem' },
                color: 'text.secondary'
              }}
              />
            </Box>
          </Box>
        </Box>

        <Collapse in={isExpanded} timeout='auto' unmountOnExit>
          <Box component='form' onSubmit={handleSubmit(onSubmit)}>
            <Grid2 container spacing={{ xs: 1.5, sm: 2 }}>
              <Grid2 size={{ xs: 6, sm: 6, md: 3 }}>
                <TextFieldElement
                  name='min_distance_miles'
                  label='Min Distance (miles)'
                  control={control}
                  type='number'
                  inputProps={{ step: '0.1', min: '0' }}
                  fullWidth
                  size='small'
                  variant='outlined'
                />
              </Grid2>

              <Grid2 size={{ xs: 6, sm: 6, md: 3 }}>
                <TextFieldElement
                  name='max_distance_miles'
                  label='Max Distance (miles)'
                  control={control}
                  type='number'
                  inputProps={{ step: '0.1', min: '0' }}
                  fullWidth
                  size='small'
                  variant='outlined'
                />
              </Grid2>

              <Grid2 size={{ xs: 6, sm: 6, md: 3 }}>
                <TextFieldElement
                  name='min_pay_per_mile'
                  label='Min Pay Per Mile ($)'
                  control={control}
                  type='number'
                  inputProps={{ step: '0.01', min: '0' }}
                  fullWidth
                  size='small'
                  variant='outlined'
                />
              </Grid2>

              <Grid2 size={{ xs: 6, sm: 6, md: 3 }}>
                <Autocomplete
                  options={storeOptions}
                  value={selectedStore}
                  onChange={handleStoreChange}
                  getOptionLabel={(option) => option.label || ''}
                  isOptionEqualToValue={(option, value) => option.value === value?.value}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label='Store'
                      size='small'
                      variant='outlined'
                      fullWidth
                    />
                  )}
                  clearOnEscape
                  clearIcon={<ClearIcon />}
                />
              </Grid2>
            </Grid2>

            <Box sx={{
              display: 'flex',
              gap: { xs: 1, sm: 1.5 },
              mt: { xs: 2, sm: 3 },
              justifyContent: { xs: 'space-between', sm: 'flex-end' },
              flexDirection: 'row'
            }}
            >
              <Button
                variant='outlined'
                onClick={handleClearFilters}
                startIcon={<ClearIcon />}
                size='small'
                sx={{
                  flex: { xs: 1, sm: 'none' },
                  minWidth: { xs: 'auto', sm: 120 },
                  fontSize: { xs: '0.8rem', sm: '0.875rem' }
                }}
              >
                Clear Filters
              </Button>
              <Button
                type='submit'
                variant='contained'
                startIcon={<SearchIcon />}
                size='small'
                sx={{
                  flex: { xs: 1, sm: 'none' },
                  minWidth: { xs: 'auto', sm: 120 },
                  fontSize: { xs: '0.8rem', sm: '0.875rem' }
                }}
              >
                Apply Filters
              </Button>
            </Box>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  )
}

const mapStateToProps = (state) => ({
  stores: storeSelectors.getAllStores(state)
})

export default connect(mapStateToProps)(OffersFilter)
