import React, { useState, useEffect } from 'react'
import {
  Box,
  Tabs,
  Tab,
  Container,
  CircularProgress
} from '@mui/material'
import { connect } from 'react-redux'
import API from 'data/api'
import { actions as entityActions } from 'data/entities'
import { ENTITY_TYPES } from 'data/entities/constants'

import { selectors as scheduleSelectors } from 'data/schedule'
import { CustomTimeRangeManager } from './components'

const ScheduleScreen = ({
  days,
  ranges,
  fetchDays,
  fetchRanges,
  fetchRangesByDay,
  clearRanges,
  createRange,
  updateRange,
  deleteRange
}) => {
  const [selectedDayIndex, setSelectedDayIndex] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      try {
        await fetchDays()
        await fetchRanges()
      } catch (error) {
        console.error('Error loading schedule data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  // Load ranges for selected day when day changes
  useEffect(() => {
    if (days.length > 0 && selectedDayIndex >= 0) {
      const selectedDay = days[selectedDayIndex]
      if (selectedDay) {
        // Clear existing ranges first, then fetch ranges filtered by selected day
        clearRanges()
        fetchRangesByDay(selectedDay.id)
      }
    }
  }, [selectedDayIndex])

  const handleTabChange = (_, newValue) => {
    setSelectedDayIndex(newValue)
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  const selectedDay = days[selectedDayIndex]

  return (
    <Container maxWidth='md' sx={{ mt: 2, px: 2 }}>
      <Box
        sx={{
          backgroundColor: 'white',
          borderRadius: 2,
          padding: { xs: 2, sm: 3 },
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e0e0e0',
          width: '100%'
        }}
      >

        {days.length > 0 && (
          <Box sx={{ mb: 1 }}>
            <Tabs
              value={selectedDayIndex}
              onChange={handleTabChange}
              variant='scrollable'
              scrollButtons={false}
              sx={{
                position: 'relative',
                '& .MuiTabs-indicator': {
                  display: 'none' // Hide the default indicator since we're using button style
                },
                '& .MuiTabs-root': {
                  minHeight: 'auto'
                },
                '& .MuiTab-root': {
                  minWidth: { xs: 32, sm: 48 }, // Compact minimum width
                  minHeight: { xs: 32, sm: 36 }, // Reduced height
                  mx: { xs: 0.25, sm: 0.375 }, // Tighter spacing
                  px: { xs: 1, sm: 1.5 }, // Reduced padding
                  py: { xs: 0.5, sm: 0.75 }, // Reduced vertical padding
                  fontSize: { xs: '0.7rem', sm: '0.8rem' }, // Smaller font
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  color: '#64748b',
                  backgroundColor: 'rgba(248, 250, 252, 0.8)',
                  backdropFilter: 'blur(4px)',
                  border: '1px solid rgba(226, 232, 240, 0.6)',
                  borderRadius: '16px', // Pill shape but more compact
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    backgroundColor: 'rgba(236, 253, 245, 0.9)',
                    borderColor: 'rgba(34, 197, 94, 0.3)',
                    color: '#059669',
                    boxShadow: '0 2px 8px rgba(34, 197, 94, 0.15)'
                  },
                  '&.Mui-selected': {
                    color: 'white',
                    background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%)',
                    borderColor: 'transparent',
                    fontWeight: 700,
                    boxShadow: '0 2px 8px rgba(46, 125, 50, 0.3)',
                    transform: 'translateY(-1px)',
                    '&:hover': {
                      transform: 'translateY(-1px)',
                      background: 'linear-gradient(135deg, #1b5e20 0%, #0d4715 100%)',
                      boxShadow: '0 3px 12px rgba(46, 125, 50, 0.4)'
                    }
                  }
                },
                '& .MuiTabs-flexContainer': {
                  justifyContent: { xs: 'flex-start', sm: 'center' },
                  gap: { xs: 0.25, sm: 0.5 },
                  p: { xs: 0.5, sm: 1 }
                },
                '& .MuiTabs-scroller': {
                  overflow: 'auto !important',
                  scrollbarWidth: 'none', // Firefox
                  '&::-webkit-scrollbar': {
                    display: 'none' // Chrome, Safari, Edge
                  }
                }
              }}
            >
              {days.map((day) => {
                return (
                  <Tab
                    key={day.id}
                    label={day.day}
                  />
                )
              })}
            </Tabs>
          </Box>
        )}

        <Box sx={{ mt: 4 }}>
          <CustomTimeRangeManager
            ranges={ranges}
            selectedDay={selectedDay}
            onCreateRange={createRange}
            onUpdateRange={updateRange}
            onDeleteRange={deleteRange}
          />
        </Box>
      </Box>
    </Container>
  )
}

const mapStateToProps = (state) => ({
  days: scheduleSelectors.getAllDays(state),
  ranges: scheduleSelectors.getSortedRanges(state)
})

const mapDispatchToProps = (dispatch) => ({
  fetchDays: () => dispatch(API.days.list()),
  fetchRanges: () => dispatch(API.ranges.list()),
  fetchRangesByDay: (dayId) => dispatch(API.ranges.list({ day: dayId })),
  clearRanges: () => dispatch(entityActions.clear(ENTITY_TYPES.RANGES)),
  createRange: (data) => dispatch(API.ranges.create(data)),
  updateRange: (id, data) => dispatch(API.ranges.update(id, data)),
  deleteRange: (id) => dispatch(API.ranges.delete(id))
})

export default connect(mapStateToProps, mapDispatchToProps)(ScheduleScreen)
