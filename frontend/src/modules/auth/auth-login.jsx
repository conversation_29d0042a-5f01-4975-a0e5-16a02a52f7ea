import { useEffect } from 'react'
import { StytchLogin, useStytchSession } from '@stytch/react'
import { OAuthProviders, Products } from '@stytch/vanilla-js'
import { useNavigate } from 'react-router'

import { stytchHelper } from './helpers'
import { URL_PATH_DASHBOARD } from 'constants'

const AuthLogin = () => {
  const navigate = useNavigate()
  const { session } = useStytchSession()

  useEffect(() => {
    if (session) {
      navigate(URL_PATH_DASHBOARD)
    }
  }, [navigate, session])

  const styles = {
    container: {
      width: '450px'
    },
    buttons: {
      primary: {
        backgroundColor: '#038666',
        borderColor: '#038666'
      }
    }
  }

  const redirectUrl = `${window.location.origin}${stytchHelper.loginSignupRedirectUrl}`

  const config = {
    products: [Products.emailMagicLinks, Products.oauth, Products.passwords],
    emailMagicLinksOptions: {
      loginRedirectURL: redirectUrl,
      loginExpirationMinutes: stytchHelper.sessionDurationMinutes,
      signupRedirectURL: redirectUrl,
      signupExpirationMinutes: stytchHelper.sessionDurationMinutes
    },
    oauthOptions: {
      providers: [{ type: OAuthProviders.Google }],
      loginRedirectURL: redirectUrl,
      signupRedirectURL: redirectUrl
    }
  }

  return <StytchLogin config={config} styles={styles} />
}

export default AuthLogin
