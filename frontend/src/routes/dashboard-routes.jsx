import { Navigate } from 'react-router-dom'
import HomeIcon from '@mui/icons-material/Home'
import SettingsOutlinedIcon from '@mui/icons-material/Settings'
import { Badge } from '@mui/material'

import DashboardLayout from 'layouts/dashboard-layout/dashboard-layout.jsx'
import ShiptDashboard from 'modules/shipt/shipt-dashboard.jsx'
import UncaughtErrorPage from 'pages/UncaughtErrorPage.jsx'
import ScheduleIcon from '@mui/icons-material/AccessTimeFilled'
import LoyaltyIcon from '@mui/icons-material/Loyalty'

import PrivateRoute from './private-route.jsx'
import BotSettingsScreen from 'modules/settings/bot-settings-screen.jsx'
import ScheduleScreen from 'modules/schedule/schedule-screen.jsx'
import SubscriptionScreen from 'modules/subscription/subscription-screen.jsx'

export const childrenRoutes = [
  // Set up the default path to automatically redirect '/' to '/dashboard'.
  {
    path: '/',
    element: <Navigate to='/dashboard' replace />
  },
  {
    path: '/dashboard',
    element: <ShiptDashboard />,
    icon: <HomeIcon />,
    title: 'Dashboard'
  },
  {
    path: '/schedule',
    element: <ScheduleScreen />,
    icon: <ScheduleIcon />,
    title: 'Schedule'
  },
  {
    path: '/settings',
    element: <BotSettingsScreen />,
    icon: <SettingsOutlinedIcon />,
    title: 'Settings'
  },
  {
    path: '/subscription',
    element: <SubscriptionScreen />,
    icon: <LoyaltyIcon />,
    title: 'Subscription',
    badge: <Badge color="error" variant="dot" />
  }
]

const DASHBOARD_ROUTES = {
  path: '/',
  element: <PrivateRoute><DashboardLayout /></PrivateRoute>,
  children: childrenRoutes,
  errorElement: <UncaughtErrorPage />
}

export default DASHBOARD_ROUTES
