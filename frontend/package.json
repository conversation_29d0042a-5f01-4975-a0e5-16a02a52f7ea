{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite dev --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^5.1.0", "@fontsource/poppins": "^5.1.0", "@fontsource/roboto": "^5.1.0", "@mui/icons-material": "^6.1.1", "@mui/lab": "^6.0.0-beta.12", "@mui/material": "^6.1.1", "@mui/x-date-pickers": "^6.0.0", "@reduxjs/toolkit": "^2.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stytch/react": "^19.5.2", "@stytch/vanilla-js": "^5.24.1", "@workos-inc/authkit-react": "^0.9.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "es6-error": "^4.1.1", "immutability-helper": "^3.1.1", "invariant": "^2.2.4", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "notistack": "^3.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hook-form-mui": "^7.3.0", "react-redux": "^9.1.2", "react-router": "^6.26.2", "react-router-dom": "^6.26.2", "redux-actions": "^3.0.3", "sass": "^1.79.4"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.5"}}