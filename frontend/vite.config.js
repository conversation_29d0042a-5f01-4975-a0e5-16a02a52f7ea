import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'
import fs from 'fs'

function getFoldersInDirectory (dirPath) {
  return fs.readdirSync(dirPath).filter(function (file) {
    return fs.statSync(path.join(dirPath, file)).isDirectory()
  })
}

const createPathAliases = () => {
  const rootPath = './src'
  const items = getFoldersInDirectory(path.resolve(__dirname, rootPath))

  return items.reduce((aliases, item) => {
    aliases[item] = path.resolve(__dirname, `${rootPath}/${item}`)
    return aliases
  }, {})
}

export default ({ mode }) => {
  return defineConfig({
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        ...createPathAliases()
      }
    },
    server: {
      port: 8001,
      host: '0.0.0.0', // Ensure the dev server is accessible from outside the container
      hmr: {
        port: 24678 // The HMR port (exposed in Docker)
      }
    },
    watch: {
      usePolling: true // Enables polling which is more reliable in Docker environments
    },
    build: {
      outDir: 'express/dist'
    }
  })
}
