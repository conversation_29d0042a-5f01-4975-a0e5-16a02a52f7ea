const express = require('express')
const path = require('path')
const app = express()

// Serve static files from the new custom build directory
app.use(express.static(path.join(__dirname, 'dist')))

app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/index.html'))
})

const port = process.env.PORT || 8001
app.listen(port, () => {
  console.log(`Server is running on port http://localhost:${port}`)
})
