services:
  django:
    build:
      context: .
      dockerfile: ./compose/django/Dockerfile.dev
    env_file: env.dev_base
    volumes:
      - .:/shiptbot
    ports:
      - "0.0.0.0:8000:8000"
    stdin_open: true
    tty: true
  celerybeat:
    build:
      dockerfile: ./compose/django/Dockerfile.dev
    env_file: env.dev_base
    entrypoint: bash -c "celery -A shiptbot.taskapp beat -l INFO -S django_celery_beat.schedulers:DatabaseScheduler"
  celeryworker:
    build:
      dockerfile: ./compose/django/Dockerfile.dev
    env_file: env.dev_base
    entrypoint: bash -c "celery -A shiptbot.taskapp worker -l INFO  -Q priority_queue,default"
  db:
    environment:
      POSTGRES_PASSWORD: shiptbot_letmein
  frontend:
    build:
      context: .
      dockerfile: ./compose/frontend/Dockerfile.dev
    env_file: ./frontend/.env
    volumes:
      - ./frontend/src:/app/src
    ports:
      - "0.0.0.0:8001:8001"
      - "0.0.0.0:24678:24678"
    stdin_open: true
    tty: true
