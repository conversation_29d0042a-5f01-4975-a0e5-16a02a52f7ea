FROM python:3.11

ENV PYTHONUNBUFFERED 1

RUN apt-get update && apt-get install -y \
  gdal-bin \
  python3-gdal \
  vim

# Requirements have to be pulled and installed here, otherwise caching won't work
COPY ./requirements /requirements
RUN pip install -r /requirements/${REQUIREMENTS_FILE:-dev.txt}

RUN getent group django || groupadd -r django
RUN getent passwd django || useradd -r -g django django

COPY ./compose/django/gunicorn.dev.sh /gunicorn.dev.sh
RUN sed -i 's/\r//' /gunicorn.dev.sh
RUN chmod +x /gunicorn.dev.sh && chown django /gunicorn.dev.sh

# Create a home folder for the django user. Good to have for things like ngrok.
RUN mkdir /home/<USER>
RUN chown -R django /home/<USER>

WORKDIR /app

ENTRYPOINT ["/gunicorn.dev.sh"]
