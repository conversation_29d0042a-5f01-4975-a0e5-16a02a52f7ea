FROM python:3.11

ENV PYTHONUNBUFFERED 1

RUN apt-get update && apt-get install -y \
    gdal-bin \
    python3-gdal \
    vim

# Requirements have to be pulled and installed here, otherwise caching won't work
COPY ./requirements /requirements

RUN pip install -r /requirements/production.txt


RUN getent group django || groupadd -r django
RUN getent passwd django || useradd -r -g django django

COPY . /app
RUN chown -R django /app

COPY ./compose/django/celery_beat.sh /celery_beat.sh
RUN sed -i 's/\r//' /celery_beat.sh
RUN chmod +x /celery_beat.sh && chown django /celery_beat.sh

WORKDIR /app

ENTRYPOINT ["/celery_beat.sh"]
