FROM python:3.11

ENV PYTHONUNBUFFERED 1

RUN apt-get update && apt-get install -y \
    gdal-bin \
    python3-gdal \
    vim

# Requirements have to be pulled and installed here, otherwise caching won't work
COPY ./requirements /requirements

RUN pip install -r /requirements/production.txt


RUN getent group django || groupadd -r django
RUN getent passwd django || useradd -r -g django django

COPY . /app
RUN chown -R django /app

COPY ./compose/django/celery_worker.sh /celery_worker.sh
RUN sed -i 's/\r//' /celery_worker.sh
RUN chmod +x /celery_worker.sh && chown django /celery_worker.sh

WORKDIR /app

ENTRYPOINT ["/celery_worker.sh"]
