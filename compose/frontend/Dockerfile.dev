# Use an official node image as the base image
FROM node:24-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy the package.json and yarn.lock files
COPY ./frontend/package.json ./frontend/yarn.lock ./

# Install dependencies
RUN yarn install

# Copy the rest of the application code to the container
COPY ./frontend .

# Expose the port that the app will run on
EXPOSE 8001

# Command to run the application
CMD ["yarn", "dev"]
